#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终集成验证
确保改进的提示词系统能够正确提交给LLM
"""

import sys
import os
import json

# 添加backend目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def verify_bazi_service_integration():
    """验证BaziService集成"""
    print("=== 验证BaziService集成 ===\n")
    
    try:
        from bazi_service import BaziService
        
        # 创建BaziService实例
        bazi_service = BaziService()
        print("✅ BaziService创建成功")
        
        # 检查是否使用了增强版分析器
        analyzer_type = type(bazi_service.llm_analyzer).__name__
        print(f"📊 当前使用的分析器: {analyzer_type}")
        
        if analyzer_type == "EnhancedBaziAnalyzer":
            print("✅ 正在使用增强版分析器（集成理论验证）")
            return True
        elif analyzer_type == "BaziAnalyzer":
            print("⚠️ 正在使用标准分析器（未集成理论验证）")
            return False
        else:
            print(f"❓ 未知的分析器类型: {analyzer_type}")
            return False
            
    except Exception as e:
        print(f"❌ BaziService集成验证失败: {str(e)}")
        return False

def verify_prompt_content():
    """验证提示词内容"""
    print("\n=== 验证提示词内容 ===\n")
    
    try:
        from improved_prompt_manager import ImprovedPromptManager
        
        # 创建提示词管理器
        prompt_manager = ImprovedPromptManager()
        print("✅ ImprovedPromptManager创建成功")
        
        # 模拟八字信息
        bazi_info = """
【八字信息】
年柱：甲子（七杀）
月柱：丙寅（正印）  
日柱：戊午（日主）
时柱：壬戌（正财）
"""
        
        # 构建提示词
        prompt = prompt_manager.build_comprehensive_prompt("性格", bazi_info)
        
        # 关键内容检查
        critical_content = {
            "五行相生循环": "木→火→土→金→水→木",
            "五行相克关系": "木克土、土克水、水克火、火克金、金克木",
            "十神相生循环": "官杀→印→比劫→食伤→财→官杀",
            "禁止印生财": "印生财",
            "调候理论": "调候",
            "系统角色设定": "专业分析师",
            "质量控制要求": "质量控制",
            "输出格式要求": "输出格式"
        }
        
        all_present = True
        for content_name, content_text in critical_content.items():
            is_present = content_text in prompt
            status = "✅" if is_present else "❌"
            print(f"{status} {content_name}: {is_present}")
            if not is_present:
                all_present = False
        
        print(f"\n📊 提示词统计:")
        print(f"   - 总长度: {len(prompt)} 字符")
        print(f"   - 包含理论指导: {'理论指导' in prompt}")
        print(f"   - 包含错误禁止: {'绝对禁止' in prompt}")
        
        return all_present
        
    except Exception as e:
        print(f"❌ 提示词内容验证失败: {str(e)}")
        return False

def verify_theory_validation():
    """验证理论验证功能"""
    print("\n=== 验证理论验证功能 ===\n")
    
    try:
        from improved_prompt_manager import ImprovedPromptManager
        
        prompt_manager = ImprovedPromptManager()
        
        # 测试用例
        test_cases = [
            ("正确表述", "食神生财，财运稳定", True),
            ("印生财错误", "印生财，财运亨通", False),
            ("五行错误", "火生水调候，五行流通", False),
            ("比劫生财错误", "比劫生财，自力更生", False),
            ("正确十神循环", "官杀生印，印生比劫，比劫生食伤，食伤生财", True)
        ]
        
        all_correct = True
        for test_name, test_content, expected_valid in test_cases:
            result = prompt_manager.validate_analysis_result(test_content)
            
            if isinstance(result, dict):
                is_valid = result.get("is_valid", True)
                errors = result.get("errors", [])
                
                if is_valid == expected_valid:
                    status = "✅"
                    print(f"{status} {test_name}: 验证正确 (预期: {'有效' if expected_valid else '无效'}, 实际: {'有效' if is_valid else '无效'})")
                else:
                    status = "❌"
                    all_correct = False
                    print(f"{status} {test_name}: 验证错误 (预期: {'有效' if expected_valid else '无效'}, 实际: {'有效' if is_valid else '无效'})")
                
                if errors:
                    for error in errors:
                        print(f"   - 错误: {error}")
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 理论验证功能验证失败: {str(e)}")
        return False

def verify_api_integration():
    """验证API集成"""
    print("\n=== 验证API集成 ===\n")
    
    try:
        from enhanced_bazi_analyzer import EnhancedBaziAnalyzer
        
        # 模拟八字数据
        test_bazi_data = {
            "年柱": {"天干": "甲", "地支": "子"},
            "月柱": {"天干": "丙", "地支": "寅"},
            "日柱": {"天干": "戊", "地支": "午"},
            "时柱": {"天干": "壬", "地支": "戌"}
        }
        
        analyzer = EnhancedBaziAnalyzer(bazi_data=test_bazi_data)
        
        # 构建API请求数据（不实际发送）
        bazi_info = "测试八字信息"
        prompt = analyzer.prompt_manager.build_comprehensive_prompt("性格", bazi_info)
        
        api_data = {
            "model": "deepseek/deepseek-r1-0528:free",
            "messages": [
                {"role": "user", "content": prompt}
            ]
        }
        
        # 验证API数据结构
        checks = {
            "包含model字段": "model" in api_data,
            "model值正确": api_data.get("model") == "deepseek/deepseek-r1-0528:free",
            "包含messages字段": "messages" in api_data,
            "messages是列表": isinstance(api_data.get("messages"), list),
            "包含user消息": len(api_data.get("messages", [])) > 0 and api_data["messages"][0].get("role") == "user",
            "消息内容非空": len(api_data["messages"][0].get("content", "")) > 0,
            "提示词包含理论": "五行理论" in api_data["messages"][0]["content"]
        }
        
        all_passed = True
        for check_name, result in checks.items():
            status = "✅" if result else "❌"
            print(f"{status} {check_name}: {result}")
            if not result:
                all_passed = False
        
        # 显示API数据统计
        api_json = json.dumps(api_data, ensure_ascii=False)
        print(f"\n📊 API数据统计:")
        print(f"   - JSON大小: {len(api_json)} 字符")
        print(f"   - 提示词大小: {len(prompt)} 字符")
        print(f"   - 可以正常序列化: {len(api_json) > 0}")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ API集成验证失败: {str(e)}")
        return False

def main():
    """主验证函数"""
    print("🔍 八字分析系统最终集成验证")
    print("=" * 50)
    
    results = {
        "BaziService集成": verify_bazi_service_integration(),
        "提示词内容": verify_prompt_content(),
        "理论验证功能": verify_theory_validation(),
        "API集成": verify_api_integration()
    }
    
    print("\n" + "=" * 50)
    print("📋 验证结果总结:")
    
    all_passed = True
    for test_name, result in results.items():
        status = "✅" if result else "❌"
        print(f"{status} {test_name}: {'通过' if result else '失败'}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有验证都通过！")
        print("\n✅ 确认：改进的提示词系统能够正确提交给LLM")
        print("✅ 确认：理论错误（如'印生财'）已被彻底解决")
        print("✅ 确认：系统集成了完整的五行生克制化理论")
        print("✅ 确认：系统集成了标准十神生克关系")
        print("✅ 确认：具备实时理论正确性验证功能")
    else:
        print("❌ 部分验证失败，需要进一步检查")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
