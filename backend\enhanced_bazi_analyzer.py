#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版八字分析器
集成改进的提示词管理器，确保理论正确性
"""

import json
import os
import logging
import threading
import time
from datetime import datetime
from bazi_summary import get_bazi_info, set_bazi_data
import traceback
from LLMapi import call_api, API_URL, API_HEADERS
from timeout_config import get_timeout
from improved_prompt_manager import ImprovedPromptManager

logger = logging.getLogger(__name__)

class EnhancedBaziAnalyzer:
    """增强版八字分析器 - 集成理论正确性验证"""
    
    def __init__(self, personality_only=False, bazi_data=None, summary=None):
        """初始化增强版八字分析器
        
        Args:
            personality_only: 是否只分析性格维度
            bazi_data: 八字数据
            summary: 摘要文本，优先使用
        """
        self.personality_only = personality_only
        self.bazi_data = bazi_data
        self.summary = summary
        self.output_path = None
        self.analysis_results = {}
        self.lock = threading.Lock()
        
        # 初始化改进的提示词管理器
        self.prompt_manager = ImprovedPromptManager()
        
        print(f"✅ 增强版八字分析器初始化完成，集成理论验证功能", flush=True)
    
    def is_valid_data(self):
        """验证八字数据是否有效"""
        return self.bazi_data is not None or self.summary is not None
    
    def get_available_dimensions(self):
        """获取可用的分析维度"""
        if self.personality_only:
            return ["性格"]
        else:
            # 返回所有可用维度
            return ["日主强弱", "健康", "2025运势", "性格", "格局", "学业", "职业", "感情"]
    
    def analyze_dimension(self, dimension):
        """分析单个维度
        
        Args:
            dimension: 要分析的维度
            
        Returns:
            str: 分析结果
        """
        try:
            print(f"🧠 开始增强分析维度: {dimension}", flush=True)
            
            # 确保先设置八字数据，再获取结构化的八字信息
            if self.bazi_data:
                set_bazi_data(self.bazi_data)
            
            # 获取结构化的八字信息
            bazi_info_text = get_bazi_info()
            
            # 使用改进的提示词管理器构建提示词
            print(f"📝 使用改进的提示词管理器构建提示词", flush=True)
            prompt = self.prompt_manager.build_comprehensive_prompt(dimension, bazi_info_text)
            
            # 记录提示词长度和关键信息
            print(f"📊 提示词长度: {len(prompt)} 字符", flush=True)
            print(f"🔍 包含五行理论指导: {'五行理论' in prompt}", flush=True)
            print(f"🔍 包含十神理论指导: {'十神理论' in prompt}", flush=True)
            
            # 调用LLM API进行分析
            api_data = {
                "model": "deepseek/deepseek-r1-0528:free",
                "messages": [
                    {"role": "user", "content": prompt}
                ]
            }
            
            # 记录提示词到日志
            self._log_prompt(dimension, prompt)
            
            print(f"🌐 调用LLM API进行分析...", flush=True)
            api_result = call_api(API_URL, API_HEADERS, json.dumps(api_data))
            
            # 处理结果
            if "error" in api_result:
                error_msg = f"分析失败: {api_result['error']}"
                print(f"❌ {error_msg}", flush=True)
                return error_msg
            
            if "choices" in api_result and len(api_result["choices"]) > 0:
                analysis_content = api_result["choices"][0]["message"]["content"]
                
                # 使用改进的提示词管理器验证分析结果
                print(f"🔍 验证分析结果的理论正确性...", flush=True)
                validation_result = self.prompt_manager.validate_analysis_result(analysis_content)

                # 处理验证结果（兼容不同的返回格式）
                has_errors = False
                errors = []

                if isinstance(validation_result, dict):
                    if "has_errors" in validation_result:
                        has_errors = validation_result["has_errors"]
                        errors = validation_result.get("errors", [])
                    elif "is_valid" in validation_result:
                        has_errors = not validation_result["is_valid"]
                        errors = validation_result.get("errors", [])

                if has_errors and errors:
                    print(f"⚠️ 发现理论错误:", flush=True)
                    for error in errors:
                        print(f"   - {error}", flush=True)

                    # 在分析结果中添加警告信息
                    warning_text = "\n\n---\n**理论验证警告**：\n"
                    for error in errors:
                        warning_text += f"- {error}\n"
                    analysis_content += warning_text
                else:
                    print(f"✅ 理论验证通过，无错误发现", flush=True)
                
                # 清理和格式化内容
                cleaned_content = self._clean_analysis_content(analysis_content)
                
                print(f"✅ 维度 {dimension} 分析完成", flush=True)
                return cleaned_content
            else:
                error_msg = "API返回格式错误"
                print(f"❌ {error_msg}", flush=True)
                return error_msg
                
        except Exception as e:
            error_msg = f"分析过程出错: {str(e)}"
            print(f"❌ {error_msg}", flush=True)
            logger.error(f"分析维度 {dimension} 时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return error_msg
    
    def analyze_all_dimensions(self):
        """分析所有维度"""
        dimensions = self.get_available_dimensions()
        results = {}
        
        print(f"🚀 开始分析 {len(dimensions)} 个维度", flush=True)
        
        # 并行分析各维度
        threads = []
        for dimension in dimensions:
            thread = threading.Thread(
                target=self._analyze_dimension_thread,
                args=(dimension, results),
                name=f"Thread-{dimension}"
            )
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        print(f"✅ 所有维度分析完成", flush=True)
        return results
    
    def _analyze_dimension_thread(self, dimension, results):
        """线程安全的维度分析"""
        try:
            result = self.analyze_dimension(dimension)
            with self.lock:
                results[dimension] = result
        except Exception as e:
            error_msg = f"线程分析维度 {dimension} 出错: {str(e)}"
            print(f"❌ {error_msg}", flush=True)
            with self.lock:
                results[dimension] = error_msg
    
    def _log_prompt(self, dimension, prompt):
        """记录提示词到日志文件"""
        try:
            # 创建日志目录
            log_dir = os.path.join(os.path.dirname(__file__), "llm_logs")
            os.makedirs(log_dir, exist_ok=True)
            
            # 生成日志文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_filename = f"enhanced_prompt_{dimension}_{timestamp}.txt"
            log_path = os.path.join(log_dir, log_filename)
            
            # 写入提示词
            with open(log_path, 'w', encoding='utf-8') as f:
                f.write(f"=== 增强版提示词日志 ===\n")
                f.write(f"维度: {dimension}\n")
                f.write(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"提示词长度: {len(prompt)} 字符\n")
                f.write(f"包含理论指导: 是\n")
                f.write(f"{'='*50}\n\n")
                f.write(prompt)
                
        except Exception as e:
            print(f"⚠️ 记录提示词日志失败: {str(e)}", flush=True)
    
    def _clean_analysis_content(self, content):
        """清理和格式化分析内容"""
        if not content:
            return "分析内容为空"
        
        # 移除可能的JSON格式包装
        content = content.strip()
        
        # 处理可能的代码块格式
        if content.startswith('```') and content.endswith('```'):
            content = content[3:-3].strip()
            
            # 如果是JSON格式，尝试解析
            try:
                parsed_content = json.loads(content)
                if isinstance(parsed_content, dict):
                    # 提取主要内容
                    if "analysis" in parsed_content:
                        content = parsed_content["analysis"]
                    elif "content" in parsed_content:
                        content = parsed_content["content"]
                    elif len(parsed_content) == 1:
                        key = list(parsed_content.keys())[0]
                        content = parsed_content[key]
            except json.JSONDecodeError:
                pass  # 不是JSON格式，保持原样
        
        return content.strip()
    
    def get_theory_summary(self):
        """获取理论要点摘要"""
        return self.prompt_manager.get_theory_summary()
