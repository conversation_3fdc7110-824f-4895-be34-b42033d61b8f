#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
八字服务模块
处理八字分析请求并返回结果
"""

import json
import os
import logging
import traceback
import requests
import threading
import time
from datetime import datetime, timedelta
from urllib.parse import quote

from bazi_analyzer import BaziAnalyzer
from enhanced_bazi_analyzer import EnhancedBaziAnalyzer
from improved_prompt_manager import ImprovedPromptManager
from request_manager import RequestManager
from config_manager import get_config
from bazi_summary import BaziSummaryGenerator
from timeout_config import get_timeout

logger = logging.getLogger(__name__)

class BaziService:
    """八字数据处理服务"""
    
    def __init__(self):
        self.results = {}  # 存储处理结果（按请求ID）
        self.processing_status = {}  # 存储处理状态（按请求ID）

        # 添加线程安全锁
        self.status_lock = threading.Lock()

        # 固定的API域名和参数，严格按照要求不可修改
        self.base_url = "https://bzapi2.iwzbz.com/getbasebz7.php"
        self.fixed_params = "today=undefined-NaN-NaN%20NaN:NaN:00&vip=0&userguid=&yzs=0"
        # 初始化摘要生成器
        self.summary_generator = BaziSummaryGenerator()
        # 初始化配置
        self.config = get_config()

        # 选择分析器：优先使用增强版分析器
        use_enhanced_analyzer = True  # 可以通过配置控制

        if use_enhanced_analyzer:
            print(f"🚀 使用增强版八字分析器（集成理论验证）", flush=True)
            self.llm_analyzer = EnhancedBaziAnalyzer(personality_only=self.config.is_personality_only())
        else:
            print(f"🚀 使用标准八字分析器", flush=True)
            self.llm_analyzer = BaziAnalyzer(personality_only=self.config.is_personality_only())

        # 初始化改进的提示词管理器
        self.improved_prompt_manager = ImprovedPromptManager()
        # 初始化请求管理器
        self.request_manager = RequestManager()
        # 持久化存储文件路径
        self.results_file = 'bazi_results.json'
        # 加载已保存的结果
        self._load_results()

        # 启动状态清理定时器
        self._start_cleanup_timer()
    
    def generate_standard_bazi_url(self, year, month, day, hour, minute, gender):
        """根据AI提示词要求生成标准八字API链接
        
        参数:
        - year: 年份 (YYYY)
        - month: 月份 (M 或 MM)
        - day: 日期 (D 或 DD)
        - hour: 小时 (H 或 HH)
        - minute: 分钟 (M 或 MM)
        - gender: 性别 ('男' 或 '女')
        
        返回标准格式链接:
        https://bzapi2.iwzbz.com/getbasebz7.php?d=[日期时间]&s=[性别]&today=undefined-NaN-NaN%20NaN:NaN:00&vip=0&userguid=&yzs=0
        """
        try:
            # 确保所有参数都是字符串类型
            year = str(year)
            month = str(month)
            day = str(day)
            hour = str(hour)
            minute = str(minute)
            
            # 1. 格式化日期时间为标准格式：YYYY-MM-DD HH:MM
            # 确保月份和日期为两位数格式
            month_str = month.zfill(2)
            day_str = day.zfill(2)
            hour_str = hour.zfill(2)
            minute_str = minute.zfill(2)
            datetime_str = f"{year}-{month_str}-{day_str} {hour_str}:{minute_str}"
            
            # 2. URL编码日期时间（空格变为%20）
            encoded_datetime = quote(datetime_str, safe='-:')
            
            # 3. 转换性别参数：1代表男性，0代表女性
            gender_param = '1' if gender == '男' else '0'
            
            # 4. 拆分固定参数并正确拼接
            # 确保固定参数格式正确
            fixed_params_parts = self.fixed_params.split('&')
            fixed_params_dict = {}
            for part in fixed_params_parts:
                if '=' in part:
                    key, value = part.split('=', 1)
                    fixed_params_dict[key] = value
            
            # 5. 手动构建完整URL，确保参数正确拼接
            standard_url = f"{self.base_url}?d={encoded_datetime}&s={gender_param}"
            
            # 添加固定参数
            for key, value in fixed_params_dict.items():
                standard_url += f"&{key}={value}"
            
            print(f"📋 生成标准八字API链接:", flush=True)
            print(f"   原始日期时间: {datetime_str}", flush=True)
            print(f"   编码后日期时间: {encoded_datetime}", flush=True)
            print(f"   性别参数: {gender_param} ({'男' if gender_param == '1' else '女'})", flush=True)
            print(f"   完整链接: {standard_url}", flush=True)
            
            logger.info(f"生成标准八字API链接: {standard_url}")
            return standard_url
            
        except Exception as e:
            error_msg = f"生成标准URL时出错: {str(e)}"
            print(f"❌ {error_msg}", flush=True)
            logger.error(error_msg)
            raise
    
    def validate_datetime_input(self, year, month, day, hour, minute):
        """验证日期时间输入的有效性"""
        try:
            # 验证年份
            year_int = int(year)
            if year_int < 1900 or year_int > 2100:
                return False, "年份必须在1900-2100之间"
            
            # 验证月份
            month_int = int(month)
            if month_int < 1 or month_int > 12:
                return False, "月份必须在1-12之间"
            
            # 验证日期
            day_int = int(day)
            if day_int < 1 or day_int > 31:
                return False, "日期必须在1-31之间"
            
            # 验证小时
            hour_int = int(hour)
            if hour_int < 0 or hour_int > 23:
                return False, "小时必须在0-23之间"
            
            # 验证分钟
            minute_int = int(minute)
            if minute_int < 0 or minute_int > 59:
                return False, "分钟必须在0-59之间"
            
            return True, "日期时间格式正确"
            
        except ValueError:
            return False, "日期时间必须为数字格式"
    
    def process_bazi_async(self, card_key, bazi_url):
        """异步处理八字请求
        
        返回:
            request_id: 唯一请求ID
        """
        # 生成唯一请求ID
        request_id = self.request_manager.generate_request_id(card_key)
        
        # 在新线程中处理请求
        thread = threading.Thread(
            target=self._process_bazi_request,
            args=(request_id, card_key, bazi_url)
        )
        thread.daemon = True
        thread.start()
        
        return request_id
    
    def _process_bazi_request(self, request_id, card_key, bazi_url):
        """处理八字请求的内部方法"""
        try:
            print(f"\n=== 开始处理八字请求 ===", flush=True)
            print(f"请求ID: {request_id}", flush=True)
            print(f"卡密: {card_key}", flush=True)
            print(f"标准API链接: {bazi_url}", flush=True)
            print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", flush=True)
            
            logger.info(f"开始处理八字请求，请求ID: {request_id}, 卡密: {card_key}")
            
            # 标记请求开始处理
            self.request_manager.start_request(request_id)
            
            # 更新状态为处理中
            self.processing_status[request_id] = {
                'status': 'processing',
                'start_time': datetime.now(),
                'completed': False,
                'card_key': card_key,
                'request_id': request_id
            }
            
            print(f"状态已更新为: 处理中", flush=True)
            
            # 发送请求到八字API
            print(f"正在调用标准八字分析API...", flush=True)
            response = self._fetch_bazi_data(bazi_url)
            
            if response['success']:
                print(f"✅ 八字分析完成！", flush=True)
                print(f"结果数据大小: {len(str(response['data']))} 字符", flush=True)

                # 立即保存基础八字数据到请求管理器
                try:
                    print(f"💾 保存基础八字数据到请求管理器...", flush=True)
                    self.request_manager.save_basic_data(request_id, response['data'])
                    print(f"✅ 基础八字数据已保存", flush=True)
                except Exception as e:
                    print(f"⚠️ 保存基础数据时出错: {str(e)}", flush=True)
                    logger.error(f"保存基础八字数据失败: {str(e)}")

                # 生成八字摘要
                try:
                    print(f"📝 正在生成八字摘要...", flush=True)
                    summary_result = self.summary_generator.generate_summary(
                        response['data'], card_key, save_to_file=True
                    )
                    print(f"✅ 八字摘要生成成功", flush=True)
                    if 'file_path' in summary_result:
                        print(f"📄 摘要文件: {summary_result['file_path']}", flush=True)
                except Exception as e:
                    print(f"⚠️ 生成摘要时出错: {str(e)}", flush=True)
                    logger.error(f"生成八字摘要失败，卡密: {card_key}, 错误: {str(e)}")
                    summary_result = None
                
                # 进行LLM深度分析
                llm_analysis_result = None
                try:
                    print(f"🤖 开始LLM深度分析...", flush=True)
                    
                    # 更新状态：开始LLM分析
                    self.processing_status[request_id].update({
                        'status': 'llm_analyzing',
                        'llm_progress': {
                            'stage': 'initializing',
                            'message': '正在初始化LLM分析...',
                            'progress': 0
                        }
                    })
                    
                    # 创建新的LLM分析器实例，传入八字数据和摘要
                    print(f"🔧 调试信息 - 创建新的LLM分析器实例，传入八字数据和摘要", flush=True)
                    summary_text = None
                    if summary_result:
                        if 'summary' in summary_result:
                            summary_text = summary_result['summary']
                            print(f"✅ 成功获取摘要文本用于分析，长度: {len(summary_text)} 字符", flush=True)
                        elif 'file_path' in summary_result:
                            # 如果没有直接的摘要文本，但有文件路径，尝试从文件加载
                            try:
                                with open(summary_result['file_path'], 'r', encoding='utf-8') as f:
                                    summary_text = f.read()
                                print(f"✅ 成功从文件加载摘要文本，长度: {len(summary_text)} 字符", flush=True)
                            except Exception as file_e:
                                print(f"⚠️ 从文件加载摘要文本失败: {str(file_e)}", flush=True)
                                logger.error(f"从文件加载摘要文本失败: {str(file_e)}")
                    
                    self.llm_analyzer = BaziAnalyzer(
                        personality_only=self.config.is_personality_only(), 
                        bazi_data=response['data'],
                        summary=summary_text
                    )
                    
                    # 检查数据有效性
                    self.processing_status[request_id]['llm_progress'].update({
                        'stage': 'validating',
                        'message': '正在验证八字数据...',
                        'progress': 10
                    })
                    
                    # 调试：检查八字数据验证
                    print(f"🔧 调试信息 - 开始验证八字数据: {response['data']}", flush=True)
                    
                    # 验证八字数据
                    is_valid = self.llm_analyzer.is_valid_data()
                    print(f"🔧 调试信息 - 八字数据验证结果: {is_valid}", flush=True)
                    print(f"🔧 调试信息 - 分析器中的八字数据: {self.llm_analyzer.bazi_data is not None}", flush=True)
                    
                    if is_valid:
                        print(f"✅ 八字数据验证通过，开始分析...", flush=True)
                        
                        # 调试：检查LLM分析器状态
                        print(f"🔧 调试信息 - LLM分析器状态:", flush=True)
                        print(f"   - 分析器对象: {self.llm_analyzer}", flush=True)
                        print(f"   - 分析器类型: {type(self.llm_analyzer)}", flush=True)
                        
                        # 获取可用维度（性格模式下只分析性格维度）
                        try:
                            dimensions = self.llm_analyzer.get_available_dimensions()
                            print(f"🔧 调试信息 - 获取维度成功: {dimensions}", flush=True)
                        except Exception as e:
                            print(f"❌ 调试信息 - 获取维度失败: {str(e)}", flush=True)
                            raise e
                        
                        print(f"📊 可分析维度: {dimensions}", flush=True)
                        
                        self.processing_status[request_id]['llm_progress'].update({
                            'stage': 'analyzing',
                            'message': f'正在分析 {len(dimensions)} 个维度...',
                            'progress': 20,
                            'dimensions': dimensions,
                            'current_dimension': None,
                            'completed_dimensions': []
                        })
                        
                        # 执行多线程并发分析
                        if dimensions:
                            total_dimensions = len(dimensions)
                            print(f"🚀 启动多线程并发分析: {total_dimensions}个维度", flush=True)
                            
                            # 定义线程安全的进度回调函数
                            def progress_callback(completed, total, current_dim):
                                progress = 20 + (completed * 60 // total)

                                # 使用锁保护状态更新
                                with self.status_lock:
                                    # 安全地更新completed_dimensions数组
                                    if current_dim and current_dim not in self.processing_status[request_id]['llm_progress']['completed_dimensions']:
                                        self.processing_status[request_id]['llm_progress']['completed_dimensions'].append(current_dim)

                                    self.processing_status[request_id]['llm_progress'].update({
                                        'message': f'正在并发分析... ({completed}/{total}) - 当前: {current_dim}',
                                        'progress': progress,
                                        'current_dimension': current_dim,
                                        'completed_count': completed,
                                        'total_count': total
                                    })
                                print(f"📈 分析进度: {completed}/{total} ({progress}%) - 当前: {current_dim}", flush=True)
                            
                            # 设置输出文件路径
                            analysis_dir = os.path.join(os.path.dirname(__file__), "analysis_results")
                            os.makedirs(analysis_dir, exist_ok=True)
                            
                            # 构建基于出生时间和性别的文件名
                            try:
                                birth_time = response['data'].get('bz', {}).get('8', '')
                                gender = '男' if response['data'].get('sex') == 1 else '女'
                                
                                # 清理出生时间字符串，移除特殊字符
                                clean_birth_time = birth_time.replace('年', '').replace('月', '').replace('日', '').replace(' ', '_').replace('时', '')
                                filename = f"llm_analysis_{clean_birth_time}_{gender}"
                            except Exception as e:
                                print(f"⚠️ 构建文件名失败，使用默认格式: {str(e)}", flush=True)
                                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                                filename = f"llm_analysis_{card_key}_{timestamp}"
                            
                            analysis_file = os.path.join(analysis_dir, f"{filename}.json")
                            self.llm_analyzer.output_path = analysis_file
                            
                            try:
                                # 使用多线程批量分析（一次性并发全部）
                                optimal_concurrency = min(len(dimensions), 30)  # 进一步提升并发数实现真正的一次性并发全部
                                print(f"🔧 使用并发数: {optimal_concurrency} (一次性并发全部维度，最大支持30个)", flush=True)
                                
                                analysis_results = self.llm_analyzer.batch_analyze_with_queue(
                                    dimensions=dimensions,
                                    concurrency=optimal_concurrency,
                                    progress_callback=progress_callback
                                )
                                
                                print(f"✅ 多线程分析完成，成功分析 {len(analysis_results)} 个维度", flush=True)
                                
                            except Exception as e:
                                print(f"❌ 多线程分析失败: {str(e)}", flush=True)
                                # 降级到单线程分析
                                print(f"🔄 降级到单线程分析...", flush=True)
                                analysis_results = {}
                                for i, dimension in enumerate(dimensions):
                                    try:
                                        progress = 20 + (i * 60 // total_dimensions)
                                        self.processing_status[request_id]['llm_progress'].update({
                                            'message': f'单线程分析 {dimension} 维度...',
                                            'progress': progress,
                                            'current_dimension': dimension
                                        })
                                        result = self.llm_analyzer.analyze_dimension(dimension)
                                        analysis_results[dimension] = result
                                        print(f"✅ {dimension} 维度分析完成", flush=True)
                                    except Exception as dim_e:
                                        print(f"❌ 分析维度 {dimension} 失败: {str(dim_e)}", flush=True)
                                        analysis_results[dimension] = f"分析失败: {str(dim_e)}"
                            
                            # 更新状态：完成分析
                            self.processing_status[request_id]['llm_progress'].update({
                                'stage': 'completed',
                                'message': '多线程分析已完成',
                                'progress': 100
                            })
                            
                            # 检查分析文件是否存在（多线程分析已自动保存）
                            if not os.path.exists(analysis_file):
                                # 如果文件不存在，手动保存结果
                                print(f"⚠️ 分析文件不存在，手动保存结果到: {analysis_file}", flush=True)
                                with open(analysis_file, 'w', encoding='utf-8') as f:
                                    json.dump(analysis_results, f, ensure_ascii=False, indent=2)
                            else:
                                print(f"✅ 分析结果已保存到: {analysis_file}", flush=True)

                            
                            llm_analysis_result = {
                                'success': True,
                                'results': analysis_results,
                                'file_path': analysis_file,
                                'dimensions': dimensions,
                                'analysis_time': datetime.now().isoformat()
                            }
                            
                            print(f"✅ LLM分析完成，结果已保存到: {analysis_file}", flush=True)
                        else:
                            print(f"⚠️ 没有可分析的维度", flush=True)
                            self.processing_status[request_id]['llm_progress'].update({
                                'stage': 'error',
                                'message': '没有可分析的维度',
                                'progress': 0
                            })
                            llm_analysis_result = {'success': False, 'error': '没有可分析的维度'}
                    else:
                        print(f"❌ 八字数据验证失败，无法进行LLM分析", flush=True)
                        self.processing_status[request_id]['llm_progress'].update({
                            'stage': 'error',
                            'message': '八字数据验证失败',
                            'progress': 0
                        })
                        llm_analysis_result = {'success': False, 'error': '八字数据验证失败'}
                        
                except Exception as e:
                    print(f"⚠️ LLM分析时出错: {str(e)}", flush=True)
                    logger.error(f"LLM分析失败，卡密: {card_key}, 错误: {str(e)}")
                    
                    # 更新错误状态
                    self.processing_status[request_id]['llm_progress'].update({
                        'stage': 'error',
                        'message': f'LLM分析出错: {str(e)}',
                        'progress': 0
                    })
                    
                    llm_analysis_result = {'success': False, 'error': str(e)}
                
                # 处理成功
                self.results[request_id] = {
                    'success': True,
                    'data': response['data'],
                    'summary': summary_result,
                    'llm_analysis': llm_analysis_result,
                    'processed_time': datetime.now().isoformat(),
                    'bazi_url': bazi_url,
                    'card_key': card_key,
                    'request_id': request_id
                }
                
                # 保存结果到文件
                self._save_results()
                
                # 标记请求完成
                self.request_manager.complete_request(request_id)
                
                # 更新状态为完成
                if request_id in self.processing_status:
                    self.processing_status[request_id]['status'] = 'completed'
                    self.processing_status[request_id]['completed'] = True
                    self.processing_status[request_id]['end_time'] = datetime.now().isoformat()
                    # 清除LLM进度信息
                    if 'llm_progress' in self.processing_status[request_id]:
                        del self.processing_status[request_id]['llm_progress']
                else:
                    # 如果状态不存在，创建完成状态
                    self.processing_status[request_id] = {
                        'status': 'completed',
                        'completed': True,
                        'start_time': datetime.now().isoformat(),
                        'end_time': datetime.now().isoformat(),
                        'card_key': card_key,
                        'request_id': request_id
                    }
                
                print(f"✅ 处理完成，请求ID: {request_id}, 卡密: {card_key}", flush=True)
                print(f"完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", flush=True)
                logger.info(f"八字请求处理完成，请求ID: {request_id}, 卡密: {card_key}")
            else:
                print(f"❌ 八字分析失败: {response['error']}", flush=True)
                
                # 处理失败
                self.results[request_id] = {
                    'success': False,
                    'error': response['error'],
                    'processed_time': datetime.now().isoformat(),
                    'card_key': card_key,
                    'request_id': request_id
                }
                
                # 保存结果到文件
                self._save_results()
                
                # 标记请求失败
                self.request_manager.fail_request(request_id, response['error'])
                
                # 更新状态为失败
                if request_id in self.processing_status:
                    self.processing_status[request_id]['status'] = 'failed'
                    self.processing_status[request_id]['completed'] = True
                    self.processing_status[request_id]['error'] = response['error']
                    self.processing_status[request_id]['end_time'] = datetime.now().isoformat()
                    # 清除LLM进度信息
                    if 'llm_progress' in self.processing_status[request_id]:
                        del self.processing_status[request_id]['llm_progress']
                else:
                    # 如果状态不存在，创建失败状态
                    self.processing_status[request_id] = {
                        'status': 'failed',
                        'completed': True,
                        'error': response['error'],
                        'start_time': datetime.now().isoformat(),
                        'end_time': datetime.now().isoformat(),
                        'card_key': card_key,
                        'request_id': request_id
                    }
                
                print(f"❌ 处理失败，请求ID: {request_id}, 卡密: {card_key}, 错误: {response['error']}", flush=True)
                logger.error(f"八字请求处理失败，请求ID: {request_id}, 卡密: {card_key}, 错误: {response['error']}")
                
        except Exception as e:
            print(f"💥 处理异常: {str(e)}", flush=True)
            print(f"异常时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", flush=True)
            logger.error(f"处理八字请求时出现异常，请求ID: {request_id}, 卡密: {card_key}, 错误: {str(e)}")
            
            self.results[request_id] = {
                'success': False,
                'error': f'处理异常: {str(e)}',
                'processed_time': datetime.now().isoformat(),
                'card_key': card_key,
                'request_id': request_id
            }
            
            # 保存结果到文件
            self._save_results()
            
            # 标记请求失败
            self.request_manager.fail_request(request_id, f'处理异常: {str(e)}')
            
            # 更新状态为错误
            if request_id in self.processing_status:
                self.processing_status[request_id]['status'] = 'error'
                self.processing_status[request_id]['completed'] = True
                self.processing_status[request_id]['error'] = str(e)
                self.processing_status[request_id]['end_time'] = datetime.now().isoformat()
                # 清除LLM进度信息
                if 'llm_progress' in self.processing_status[request_id]:
                    del self.processing_status[request_id]['llm_progress']
            else:
                # 如果状态不存在，创建错误状态
                self.processing_status[request_id] = {
                    'status': 'error',
                    'completed': True,
                    'error': str(e),
                    'start_time': datetime.now().isoformat(),
                    'end_time': datetime.now().isoformat(),
                    'card_key': card_key,
                    'request_id': request_id
                }
        finally:
            print(f"=== 八字请求处理结束 ===\n", flush=True)
    
    def _fetch_bazi_data(self, bazi_url):
        """从八字API获取数据"""
        try:
            print(f"📡 正在请求八字API...", flush=True)
            print(f"🔗 请求链接: {bazi_url}", flush=True)
            logger.info(f"正在请求八字API: {bazi_url}")
            
            # 设置请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            # 发送GET请求（使用统一的超时配置）
            timeout = get_timeout('http_request')
            print(f"⏳ 发送HTTP请求中（超时{timeout}秒）...", flush=True)
            response = requests.get(bazi_url, headers=headers, timeout=timeout)
            
            print(f"📊 收到响应，状态码: {response.status_code}", flush=True)
            
            if response.status_code == 200:
                try:
                    # 解析JSON响应
                    data = response.json()
                    print(f"✅ JSON解析成功", flush=True)
                    print(f"📋 响应数据字段: {list(data.keys()) if isinstance(data, dict) else '非字典格式'}", flush=True)
                    
                    return {
                        'success': True,
                        'data': data
                    }
                except json.JSONDecodeError as e:
                    error_msg = f"JSON解析失败: {str(e)}"
                    print(f"❌ {error_msg}", flush=True)
                    print(f"📄 原始响应内容: {response.text[:200]}...", flush=True)
                    return {
                        'success': False,
                        'error': error_msg
                    }
            else:
                error_msg = f"HTTP请求失败，状态码: {response.status_code}"
                print(f"❌ {error_msg}", flush=True)
                return {
                    'success': False,
                    'error': error_msg
                }
                
        except requests.exceptions.Timeout:
            timeout = get_timeout('http_request')
            error_msg = f"请求超时（{timeout}秒）"
            print(f"⏰ {error_msg}", flush=True)
            logger.error(f"八字API请求超时: {bazi_url}")
            return {
                'success': False,
                'error': error_msg
            }
        except requests.exceptions.RequestException as e:
            error_msg = f"网络请求异常: {str(e)}"
            print(f"🌐 {error_msg}", flush=True)
            logger.error(f"八字API网络异常: {str(e)}")
            return {
                'success': False,
                'error': error_msg
            }
        except Exception as e:
            error_msg = f"处理API响应时出错: {str(e)}"
            print(f"❌ {error_msg}")
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }
    
    def get_result(self, identifier):
        """获取处理结果
        
        Args:
            identifier: 可以是请求ID或卡密
        """
        # 首先尝试作为请求ID查找
        if identifier in self.results:
            return self.results[identifier]
        
        # 如果不是请求ID，尝试作为卡密查找最新的结果
        latest_request = self.request_manager.get_latest_request_by_card(identifier)
        if latest_request:
            # latest_request是一个元组(request_id, request_info)，我们只需要request_id
            request_id = latest_request[0]
            if request_id in self.results:
                return self.results[request_id]
        
        # 备用查询：直接从结果文件中查找（处理服务器重启后RequestManager数据丢失的情况）
        # 如果identifier看起来像请求ID（包含下划线），直接查找
        if '_' in identifier and identifier in self.results:
            return self.results[identifier]
        
        # 如果identifier是卡密，查找所有以该卡密开头的请求ID
        if '_' not in identifier:  # 这是一个卡密
            matching_results = []
            for request_id, result in self.results.items():
                if request_id.startswith(identifier + '_'):
                    matching_results.append((request_id, result))
            
            # 如果找到匹配的结果，返回最新的（按时间戳排序）
            if matching_results:
                # 按请求ID中的时间戳排序，返回最新的
                matching_results.sort(key=lambda x: x[0].split('_')[1] if len(x[0].split('_')) > 1 else '0', reverse=True)
                return matching_results[0][1]
        
        return None
    
    def get_processing_status(self, identifier):
        """获取处理状态
        
        Args:
            identifier: 可以是请求ID或卡密
        """
        # 首先尝试作为请求ID查找
        if identifier in self.processing_status:
            return self.processing_status[identifier]
        
        # 如果不是请求ID，尝试作为卡密查找最新的状态
        latest_request = self.request_manager.get_latest_request_by_card(identifier)
        if latest_request:
            # latest_request是一个元组(request_id, request_info)，我们只需要request_id
            request_id = latest_request[0]
            if request_id in self.processing_status:
                return self.processing_status[request_id]
        
        # 备用查询：检查是否有对应的完成结果（处理服务器重启后RequestManager数据丢失的情况）
        result = self.get_result(identifier)
        if result:
            # 如果找到了结果，说明处理已完成
            return {
                'status': 'completed',
                'completed': True,
                'result': result
            }
        
        return {
            'status': 'not_found',
            'completed': False
        }
    
    def get_result_by_request_id(self, request_id):
        """根据请求ID获取处理结果"""
        return self.results.get(request_id, None)

    def get_basic_result(self, request_id):
        """获取基础八字数据（不包含LLM分析）

        Args:
            request_id: 请求ID

        Returns:
            dict: 基础八字数据，如果没有则返回None
        """
        try:
            print(f"🔍 [DEBUG] get_basic_result 调用，请求ID: {request_id}")

            # 检查是否有完整结果
            full_result = self.results.get(request_id)
            print(f"🔍 [DEBUG] 完整结果存在: {full_result is not None}")
            if full_result:
                print(f"🔍 [DEBUG] 返回完整结果")
                return full_result

            # 检查请求管理器中的基础数据
            request_info = self.request_manager.get_request_info(request_id)
            print(f"🔍 [DEBUG] 请求信息存在: {request_info is not None}")
            if request_info:
                print(f"🔍 [DEBUG] 请求信息键: {list(request_info.keys())}")
                print(f"🔍 [DEBUG] 有basic_data: {'basic_data' in request_info}")

            if request_info and 'basic_data' in request_info:
                basic_data = request_info['basic_data']

                # 构造基础结果格式
                basic_result = {
                    'request_id': request_id,
                    'processed_time': request_info.get('start_time', datetime.now()),
                    'data': basic_data,
                    'has_llm_analysis': False,
                    'basic_only': True
                }

                logger.info(f"找到基础八字数据，请求ID: {request_id}")
                return basic_result

            logger.info(f"未找到基础八字数据，请求ID: {request_id}")
            return None

        except Exception as e:
            logger.error(f"获取基础八字数据时出错: {e}")
            return None

    def get_status_by_request_id(self, request_id):
        """根据请求ID获取处理状态"""
        return self.processing_status.get(request_id, {
            'status': 'not_found',
            'completed': False
        })
    
    def get_all_results(self):
        """获取所有历史报告"""
        reports = []
        for request_id, result in self.results.items():
            if result and result.get('success', False):
                # 从request_id中提取卡密和时间戳
                parts = request_id.split('_')
                if len(parts) >= 2:
                    card_key = parts[0]
                    timestamp = parts[1]
                    try:
                        # 转换时间戳为可读格式
                        created_time = datetime.fromtimestamp(int(timestamp))
                        reports.append({
                            'id': request_id,
                            'cardKey': card_key,
                            'timestamp': created_time.isoformat(),
                            'data': result
                        })
                    except (ValueError, IndexError):
                        # 如果时间戳解析失败，使用当前时间
                        reports.append({
                            'id': request_id,
                            'cardKey': card_key,
                            'timestamp': datetime.now().isoformat(),
                            'data': result
                        })
        
        # 按时间戳降序排序
        reports.sort(key=lambda x: x['timestamp'], reverse=True)
        return reports
    
    def get_reports_by_card_key(self, card_key):
        """获取特定卡密的所有历史报告"""
        card_reports = []
        for request_id, result in self.results.items():
            if request_id.startswith(f"{card_key}_") and result.get('success', False):
                parts = request_id.split('_')
                if len(parts) >= 2:
                    timestamp = parts[1]
                    try:
                        # 转换时间戳为可读格式
                        created_time = datetime.fromtimestamp(int(timestamp))
                        card_reports.append({
                            'id': request_id,
                            'cardKey': card_key,
                            'timestamp': created_time.isoformat(),
                            'data': result
                        })
                    except (ValueError, IndexError):
                        # 如果时间戳解析失败，使用当前时间
                        card_reports.append({
                            'id': request_id,
                            'cardKey': card_key,
                            'timestamp': datetime.now().isoformat(),
                            'data': result
                        })
        
        # 按时间戳降序排序
        card_reports.sort(key=lambda x: x['timestamp'], reverse=True)
        return card_reports
    
    def cleanup_old_results(self, hours=24):
        """清理旧的结果数据"""
        current_time = datetime.now()
        
        # 清理结果数据
        keys_to_remove = []
        for key, result in self.results.items():
            if 'processed_time' in result:
                time_diff = current_time - result['processed_time']
                if time_diff.total_seconds() > hours * 3600:
                    keys_to_remove.append(key)
        
        for key in keys_to_remove:
            del self.results[key]
            if key in self.processing_status:
                del self.processing_status[key]
        
        logger.info(f"清理了 {len(keys_to_remove)} 个旧结果")
        # 保存更新后的结果
        self._save_results()
        
    def _load_results(self):
        """从文件加载已保存的结果"""
        try:
            if os.path.exists(self.results_file):
                with open(self.results_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 恢复datetime对象
                    for key, result in data.items():
                        if 'processed_time' in result and isinstance(result['processed_time'], str):
                            result['processed_time'] = datetime.fromisoformat(result['processed_time'])
                    self.results = data
                    logger.info(f"已加载 {len(self.results)} 个保存的结果")
            else:
                logger.info("未找到保存的结果文件，从空开始")
        except Exception as e:
            logger.error(f"加载结果文件失败: {e}")
            self.results = {}
    
    def _save_results(self):
        """保存结果到文件"""
        try:
            # 准备序列化数据
            data_to_save = {}
            for key, result in self.results.items():
                result_copy = result.copy()
                # 将datetime对象转换为字符串
                if 'processed_time' in result_copy and isinstance(result_copy['processed_time'], datetime):
                    result_copy['processed_time'] = result_copy['processed_time'].isoformat()
                data_to_save[key] = result_copy
            
            with open(self.results_file, 'w', encoding='utf-8') as f:
                json.dump(data_to_save, f, ensure_ascii=False, indent=2)
            logger.info(f"已保存 {len(data_to_save)} 个结果到文件")
        except Exception as e:
            logger.error(f"保存结果文件失败: {e}")
        
    def delete_report(self, report_id):
        """删除特定的分析报告
        
        Args:
            report_id: 报告ID（请求ID）
        
        Returns:
            bool: 删除成功返回True，否则返回False
        """
        try:
            print(f"🗑️ 尝试删除报告: {report_id}", flush=True)
            
            # 检查报告是否存在
            if report_id not in self.results:
                print(f"❌ 报告不存在: {report_id}", flush=True)
                return False
            
            # 获取报告数据，用于构建文件路径
            report_data = self.results[report_id]
            
            # 删除对应的分析结果文件
            self._delete_analysis_file(report_id, report_data)
            
            # 删除报告
            del self.results[report_id]
            print(f"✅ 已从结果集删除报告: {report_id}", flush=True)
            
            # 如果存在相关的处理状态，也一并删除
            if report_id in self.processing_status:
                del self.processing_status[report_id]
            
            # 保存更新后的结果
            self._save_results()
            print(f"✅ 已从状态集删除报告: {report_id}", flush=True)
            
            logger.info(f"成功删除报告: {report_id}")
            return True
            
        except Exception as e:
            error_msg = f"删除报告时出错: {str(e)}"
            print(f"❌ {error_msg}", flush=True)
            logger.error(error_msg)
            return False
    
    def _delete_analysis_file(self, report_id, report_data):
        """删除对应的分析结果文件
        
        Args:
            report_id: 报告ID
            report_data: 报告数据
        """
        try:
            analysis_dir = os.path.join(os.path.dirname(__file__), "analysis_results")
            
            # 尝试从报告数据中获取文件路径
            file_path = None
            if 'llm_analysis' in report_data and 'file_path' in report_data['llm_analysis']:
                file_path = report_data['llm_analysis']['file_path']
            
            # 如果没有找到文件路径，尝试根据报告数据构建文件名
            if not file_path or not os.path.exists(file_path):
                # 尝试多种可能的文件名格式
                possible_filenames = []
                
                # 格式1: 基于出生时间和性别
                if 'bazi_data' in report_data and 'data' in report_data['bazi_data']:
                    bazi_data = report_data['bazi_data']['data']
                    if 'bz' in bazi_data and '8' in bazi_data['bz']:
                        birth_time = bazi_data['bz']['8']
                        gender = '男' if bazi_data.get('sex') == 1 else '女'
                        clean_birth_time = birth_time.replace('年', '').replace('月', '').replace('日', '').replace(' ', '_').replace('时', '')
                        possible_filenames.append(f"llm_analysis_{clean_birth_time}_{gender}.json")
                
                # 格式2: 基于卡密和时间戳
                if '_' in report_id:
                    parts = report_id.split('_')
                    if len(parts) >= 2:
                        card_key = parts[0]
                        timestamp = parts[1]
                        possible_filenames.append(f"llm_analysis_{card_key}_{timestamp}.json")
                
                # 格式3: 直接使用report_id
                possible_filenames.append(f"llm_analysis_{report_id}.json")
                
                # 查找匹配的文件
                for filename in possible_filenames:
                    potential_path = os.path.join(analysis_dir, filename)
                    if os.path.exists(potential_path):
                        file_path = potential_path
                        break
            
            # 删除文件
            if file_path and os.path.exists(file_path):
                os.remove(file_path)
                print(f"✅ 已删除分析结果文件: {file_path}", flush=True)
                logger.info(f"已删除分析结果文件: {file_path}")
            else:
                print(f"⚠️ 未找到对应的分析结果文件: {report_id}", flush=True)
                logger.warning(f"未找到对应的分析结果文件: {report_id}")
                
        except Exception as e:
            error_msg = f"删除分析结果文件时出错: {str(e)}"
            print(f"❌ {error_msg}", flush=True)
            logger.error(error_msg)

    def cleanup_old_status(self, max_age_hours=None):
        """清理超过指定时间的状态记录"""
        if max_age_hours is None:
            max_age_hours = get_timeout('status_max_age') / 3600  # 转换为小时

        current_time = datetime.now()
        to_remove = []

        with self.status_lock:
            for request_id, status in self.processing_status.items():
                if 'start_time' in status:
                    try:
                        start_time = datetime.fromisoformat(status['start_time'])
                        if (current_time - start_time).total_seconds() > max_age_hours * 3600:
                            to_remove.append(request_id)
                    except (ValueError, TypeError) as e:
                        # 如果时间格式有问题，也清理掉
                        print(f"⚠️ 状态时间格式错误，将被清理: {request_id}, 错误: {e}")
                        to_remove.append(request_id)

            # 清理过期状态
            for request_id in to_remove:
                del self.processing_status[request_id]
                print(f"🧹 清理过期状态: {request_id}")

        if to_remove:
            print(f"🧹 共清理了 {len(to_remove)} 个过期状态记录")

    def _start_cleanup_timer(self):
        """启动状态清理定时器"""
        def cleanup_task():
            cleanup_interval = get_timeout('cleanup_interval')
            while True:
                try:
                    time.sleep(cleanup_interval)
                    self.cleanup_old_status()
                except Exception as e:
                    print(f"❌ 状态清理任务出错: {str(e)}")
                    logger.error(f"状态清理任务出错: {str(e)}")

        cleanup_thread = threading.Thread(target=cleanup_task, daemon=True, name="StatusCleanup")
        cleanup_thread.start()
        print("🧹 状态清理定时器已启动")