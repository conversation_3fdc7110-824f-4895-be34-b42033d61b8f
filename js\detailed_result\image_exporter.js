/**
 * 图片导出功能模块
 * 用于将详细分析报告导出为长图
 */

class ImageExporter {
    constructor() {
        this.isExporting = false;
        this.exportOptions = {
            scale: 2, // 高清晰度
            useCORS: true,
            allowTaint: true,
            backgroundColor: '#ffffff',
            // 不设置固定宽度，让html2canvas自动检测
            scrollX: 0,
            scrollY: 0,
            logging: false, // 关闭调试日志
            imageTimeout: 15000, // 图片加载超时时间
            onclone: (clonedDoc) => {
                // 在克隆的文档中应用额外的样式优化
                this.optimizeClonedDocument(clonedDoc);
            }
        };
    }

    /**
     * 导出分析报告为长图
     */
    async exportAsImage(format = 'png') {
        if (this.isExporting) {
            console.log('正在导出中，请稍候...');
            return;
        }

        try {
            this.isExporting = true;
            this.showExportProgress('准备导出...');

            // 获取要导出的内容区域
            const contentElement = this.getExportContent();
            if (!contentElement) {
                throw new Error('未找到可导出的内容');
            }

            // 准备导出环境
            await this.prepareForExport(contentElement);

            this.showExportProgress('正在生成图片...');

            // 计算实际内容尺寸
            const actualOptions = this.calculateExportOptions(contentElement);

            // 使用html2canvas生成图片
            const canvas = await html2canvas(contentElement, actualOptions);

            this.showExportProgress('正在保存图片...');

            // 下载图片
            await this.downloadImage(canvas, format);

            // 显示成功提示并设置自动消失
            this.showExportProgress('导出完成！', true);

            // 立即设置3秒后自动消失
            this.autoHideTimer = setTimeout(() => {
                this.hideExportProgress();
            }, 3000);

        } catch (error) {
            console.error('导出图片失败:', error);
            this.showExportError('导出失败: ' + error.message);
        } finally {
            this.isExporting = false;
            // 恢复页面状态
            this.restoreOriginalStyles(contentElement);
        }
    }

    /**
     * 计算导出选项
     */
    calculateExportOptions(element) {
        // 获取元素的实际尺寸
        const rect = element.getBoundingClientRect();
        const computedStyle = window.getComputedStyle(element);

        // 计算实际内容宽度（包括padding）
        const paddingLeft = parseFloat(computedStyle.paddingLeft) || 0;
        const paddingRight = parseFloat(computedStyle.paddingRight) || 0;
        let actualWidth = Math.max(rect.width, element.scrollWidth) + paddingLeft + paddingRight;

        // 检测是否为手机端
        const isMobile = window.innerWidth <= 768;

        // 手机端智能宽度适配 - 基于内容实际需求
        if (isMobile) {
            if (this.customWidth && this.customWidth !== 'auto') {
                // 用户自定义宽度
                actualWidth = Math.max(actualWidth, this.customWidth);
            } else {
                // 智能适配：基于内容实际宽度，适度放大以保证清晰度
                const screenWidth = window.innerWidth;
                const contentWidth = Math.max(rect.width, element.scrollWidth);

                // 计算内容的实际最小需求宽度
                const tables = element.querySelectorAll('table, .bazi-table, .dayun-table');
                let minContentWidth = contentWidth;

                tables.forEach(table => {
                    const tableWidth = table.scrollWidth || table.offsetWidth;
                    minContentWidth = Math.max(minContentWidth, tableWidth);
                });

                // 手机端适配策略：内容宽度 + 适度放大
                let optimalWidth;
                if (minContentWidth <= 300) {
                    // 内容很窄，适度放大
                    optimalWidth = Math.min(minContentWidth * 1.8, screenWidth * 1.2);
                } else if (minContentWidth <= 500) {
                    // 中等宽度内容
                    optimalWidth = Math.min(minContentWidth * 1.4, screenWidth * 1.1);
                } else {
                    // 较宽内容，保持原有宽度或略微放大
                    optimalWidth = Math.min(minContentWidth * 1.2, screenWidth * 1.05);
                }

                // 确保不超过合理范围
                optimalWidth = Math.min(optimalWidth, 600); // 手机端最大600px
                actualWidth = Math.max(actualWidth, optimalWidth);

                console.log('手机端内容适配:', {
                    screenWidth,
                    contentWidth,
                    minContentWidth,
                    optimalWidth,
                    finalWidth: actualWidth
                });
            }
        } else if (this.customWidth) {
            // 桌面端使用自定义宽度
            actualWidth = Math.max(actualWidth, this.customWidth);
        }

        // 确保最小宽度（防止图片过窄）
        const minExportWidth = isMobile ? 320 : 800; // 手机端最小宽度进一步降低
        actualWidth = Math.max(actualWidth, minExportWidth);

        // 计算实际内容高度（包括padding）
        const paddingTop = parseFloat(computedStyle.paddingTop) || 0;
        const paddingBottom = parseFloat(computedStyle.paddingBottom) || 0;
        const actualHeight = Math.max(rect.height, element.scrollHeight) + paddingTop + paddingBottom;

        console.log('计算的导出尺寸:', {
            isMobile: isMobile,
            screenWidth: window.innerWidth,
            width: actualWidth,
            height: actualHeight,
            scrollWidth: element.scrollWidth,
            scrollHeight: element.scrollHeight,
            rectWidth: rect.width,
            rectHeight: rect.height,
            customWidth: this.customWidth
        });

        return {
            ...this.exportOptions,
            width: Math.ceil(actualWidth),
            height: Math.ceil(actualHeight)
        };
    }

    /**
     * 获取要导出的内容区域
     */
    getExportContent() {
        // 优先导出分析内容区域
        let contentElement = document.getElementById('analysisContent');
        
        if (!contentElement || contentElement.style.display === 'none') {
            // 如果分析内容不可见，导出整个结果容器
            contentElement = document.querySelector('.detailed-result-container');
        }

        if (!contentElement) {
            // 最后备选：导出整个详细容器
            contentElement = document.querySelector('.detailed-container');
        }

        return contentElement;
    }

    /**
     * 准备导出环境
     */
    async prepareForExport(element) {
        // 确保所有图片都已加载
        const images = element.querySelectorAll('img');
        const imagePromises = Array.from(images).map(img => {
            return new Promise((resolve) => {
                if (img.complete) {
                    resolve();
                } else {
                    img.onload = resolve;
                    img.onerror = resolve; // 即使加载失败也继续
                }
            });
        });

        await Promise.all(imagePromises);

        // 手机端特殊处理
        this.prepareMobileExport(element);

        // 临时调整样式以适合导出
        this.applyExportStyles(element);
    }

    /**
     * 手机端导出优化 - 基于内容实际需求
     */
    prepareMobileExport(element) {
        const isMobile = window.innerWidth <= 768;

        if (isMobile) {
            console.log('手机端导出优化：基于内容实际宽度适配');

            // 分析内容的实际宽度需求
            const screenWidth = window.innerWidth;
            const containers = element.querySelectorAll('.detailed-container, .detailed-result-container, .detailed-analysis-content');
            const tables = element.querySelectorAll('table, .bazi-table, .dayun-table');

            // 计算内容的最小宽度需求
            let minContentWidth = 300; // 基础最小宽度

            tables.forEach(table => {
                const tableWidth = table.scrollWidth || table.offsetWidth;
                minContentWidth = Math.max(minContentWidth, tableWidth);
            });

            // 智能计算最佳导出宽度 - 极度紧凑策略
            let optimalWidth;
            if (minContentWidth <= 300) {
                optimalWidth = Math.min(minContentWidth + 10, screenWidth * 0.95); // 极窄内容，最小边距
            } else if (minContentWidth <= 400) {
                optimalWidth = Math.min(minContentWidth + 5, screenWidth * 0.98); // 窄内容，几乎无边距
            } else {
                optimalWidth = Math.min(minContentWidth, 420); // 宽内容，无额外边距
            }

            // 确保在极紧凑范围内
            optimalWidth = Math.max(optimalWidth, 280);
            optimalWidth = Math.min(optimalWidth, 420);

            containers.forEach(container => {
                // 保存原始样式
                if (!container.dataset.originalStyle) {
                    container.dataset.originalStyle = container.style.cssText;
                }

                // 强制重置所有可能影响宽度的样式
                container.style.cssText = `
                    width: ${optimalWidth}px !important;
                    min-width: ${optimalWidth}px !important;
                    max-width: ${optimalWidth}px !important;
                    padding: 0 !important;
                    margin: 0 !important;
                    border: none !important;
                    transform: none !important;
                    overflow: visible !important;
                    box-sizing: border-box !important;
                    position: static !important;
                    left: auto !important;
                    right: auto !important;
                    float: none !important;
                    display: block !important;
                    background: white !important;
                `;
            });

            // 强制重置表格样式 - 完全填满容器
            tables.forEach(table => {
                if (!table.dataset.originalStyle) {
                    table.dataset.originalStyle = table.style.cssText;
                }

                // 强制表格填满整个容器宽度
                table.style.cssText = `
                    width: 100% !important;
                    min-width: 100% !important;
                    max-width: 100% !important;
                    table-layout: fixed !important;
                    border-collapse: collapse !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    font-size: 10px !important;
                    position: relative !important;
                    left: 0 !important;
                    right: 0 !important;
                `;

                // 强制重置表格单元格
                const cells = table.querySelectorAll('td, th');
                cells.forEach(cell => {
                    cell.style.cssText = `
                        padding: 2px 3px !important;
                        white-space: nowrap !important;
                        font-size: 10px !important;
                        border: 1px solid #ddd !important;
                        text-align: center !important;
                        overflow: hidden !important;
                        text-overflow: ellipsis !important;
                        margin: 0 !important;
                        width: auto !important;
                    `;
                });
            });

            // 优化文字大小
            const textElements = element.querySelectorAll('h1, h2, h3, h4, h5, p');
            textElements.forEach(el => {
                if (!el.dataset.originalStyle) {
                    el.dataset.originalStyle = el.style.cssText;
                }
                const currentFontSize = window.getComputedStyle(el).fontSize;
                if (currentFontSize) {
                    const fontSize = parseFloat(currentFontSize);
                    if (fontSize > 14) {
                        el.style.fontSize = Math.max(fontSize * 0.85, 12) + 'px';
                    }
                }
                el.style.margin = '4px 0';
                el.style.lineHeight = '1.3';
            });

            // 最终强制重置 - 确保没有任何空白
            setTimeout(() => {
                element.style.cssText = `
                    width: ${optimalWidth}px !important;
                    min-width: ${optimalWidth}px !important;
                    max-width: ${optimalWidth}px !important;
                    padding: 0 !important;
                    margin: 0 !important;
                    overflow: hidden !important;
                    position: relative !important;
                    background: white !important;
                `;

                // 强制所有子元素也不能超出容器
                const allChildren = element.querySelectorAll('*');
                allChildren.forEach(child => {
                    if (child.offsetWidth > optimalWidth) {
                        child.style.maxWidth = '100%';
                        child.style.overflow = 'hidden';
                    }
                });
            }, 100);

            console.log('手机端极度紧凑优化完成:', {
                screenWidth,
                minContentWidth,
                optimalWidth,
                containerCount: containers.length,
                tableCount: tables.length,
                finalWidth: `${optimalWidth}px`,
                spaceEfficiency: `${((minContentWidth / optimalWidth) * 100).toFixed(1)}%`
            });
        }
    }

    /**
     * 恢复原始样式
     */
    restoreOriginalStyles(element) {
        const isMobile = window.innerWidth <= 768;

        if (isMobile) {
            console.log('手机端导出完成：恢复原始样式');

            // 恢复容器样式
            const containers = element.querySelectorAll('.detailed-container, .detailed-result-container, .detailed-analysis-content');
            containers.forEach(container => {
                if (container.dataset.originalStyle) {
                    container.style.cssText = container.dataset.originalStyle;
                    delete container.dataset.originalStyle;
                }
            });

            // 恢复表格样式
            const tables = element.querySelectorAll('table, .bazi-table, .dayun-table');
            tables.forEach(table => {
                if (table.dataset.originalStyle) {
                    table.style.cssText = table.dataset.originalStyle;
                    delete table.dataset.originalStyle;
                }
            });
        }

        // 移除导出样式类
        element.classList.remove('exporting-image');
    }

    /**
     * 应用导出专用样式
     */
    applyExportStyles(element) {
        // 添加导出样式类
        element.classList.add('exporting-image');

        // 创建导出专用样式
        if (!document.getElementById('export-styles')) {
            const style = document.createElement('style');
            style.id = 'export-styles';
            style.textContent = `
                .exporting-image {
                    background: white !important;
                    min-height: auto !important;
                    padding: 20px !important;
                    box-shadow: none !important;
                    width: auto !important;
                    max-width: none !important;
                    overflow: visible !important;
                }
                
                .exporting-image .detailed-header-actions,
                .exporting-image .detailed-footer,
                .exporting-image .loading-state,
                .exporting-image .error-state,
                .exporting-image .empty-state {
                    display: none !important;
                }
                
                .exporting-image .detailed-result-header h2 {
                    text-align: center !important;
                    margin-bottom: 20px !important;
                    color: #333 !important;
                }
                
                .exporting-image .analysis-section {
                    margin-bottom: 30px !important;
                    page-break-inside: avoid !important;
                }
                
                .exporting-image .section-title {
                    color: #4a00e0 !important;
                    border-bottom: 2px solid #4a00e0 !important;
                    padding-bottom: 10px !important;
                    margin-bottom: 15px !important;
                }
                
                .exporting-image .bazi-table,
                .exporting-image .dayun-table {
                    width: 100% !important;
                    margin: 15px 0 !important;
                }
                
                .exporting-image .analysis-content {
                    line-height: 1.6 !important;
                    color: #333 !important;
                }
                
                /* 桌面端表格样式 */
                .exporting-image table {
                    border-collapse: collapse !important;
                    width: 100% !important;
                    table-layout: auto !important;
                }

                .exporting-image td, .exporting-image th {
                    border: 1px solid #ddd !important;
                    padding: 8px !important;
                    text-align: center !important;
                    white-space: nowrap !important;
                }

                /* 桌面端容器样式 */
                .exporting-image .detailed-result-container,
                .exporting-image .detailed-analysis-content,
                .exporting-image .detailed-container {
                    width: auto !important;
                    min-width: 800px !important;
                    max-width: none !important;
                    padding: 16px !important;
                    margin: 0 auto !important;
                    box-sizing: border-box !important;
                }

                /* 手机端强制覆盖 - 最高优先级 */
                @media (max-width: 768px) {
                    /* 强制重置所有容器样式 */
                    .exporting-image,
                    .exporting-image * {
                        box-sizing: border-box !important;
                    }

                    .exporting-image {
                        padding: 0 !important;
                        margin: 0 !important;
                        width: 100% !important;
                        min-width: 100% !important;
                        max-width: 100% !important;
                        overflow: hidden !important;
                        position: relative !important;
                    }

                    /* 强制覆盖容器样式 - 使用更高优先级 */
                    .exporting-image .detailed-result-container,
                    .exporting-image .detailed-analysis-content,
                    .exporting-image .detailed-container,
                    .exporting-image > .detailed-result-container,
                    .exporting-image > .detailed-analysis-content,
                    .exporting-image > .detailed-container {
                        width: 100% !important;
                        min-width: 100% !important;
                        max-width: 100% !important;
                        padding: 0 !important;
                        margin: 0 !important;
                        transform: none !important;
                        position: static !important;
                        left: auto !important;
                        right: auto !important;
                        float: none !important;
                        display: block !important;
                        overflow: visible !important;
                    }

                    /* 减少手机端内边距，紧凑布局 */
                    .exporting-image .detailed-header,
                    .exporting-image .detailed-dimension-section,
                    .exporting-image .detailed-bazi-info-item {
                        padding: 8px !important;
                        margin: 4px 0 !important;
                    }

                    /* 强制表格填满宽度 - 覆盖桌面端min-width */
                    .exporting-image table,
                    .exporting-image .bazi-table,
                    .exporting-image .dayun-table,
                    .exporting-image > * table,
                    .exporting-image * table {
                        width: 100% !important;
                        min-width: 100% !important;
                        max-width: 100% !important;
                        margin: 0 !important;
                        padding: 0 !important;
                        font-size: 10px !important;
                        table-layout: fixed !important;
                        border-collapse: collapse !important;
                        position: relative !important;
                        left: 0 !important;
                        right: 0 !important;
                    }

                    .exporting-image table td,
                    .exporting-image table th,
                    .exporting-image * table td,
                    .exporting-image * table th {
                        padding: 2px 3px !important;
                        white-space: nowrap !important;
                        font-size: 10px !important;
                        overflow: hidden !important;
                        text-overflow: ellipsis !important;
                        border: 1px solid #ddd !important;
                        margin: 0 !important;
                        width: auto !important;
                    }
                }

                /* 确保八字表格完整显示 */
                .exporting-image .bazi-table,
                .exporting-image .dayun-table {
                    min-width: 600px !important;
                    margin: 15px auto !important;
                    width: 100% !important;
                    table-layout: fixed !important;
                }
                
                /* 隐藏不必要的交互元素 */
                .exporting-image button,
                .exporting-image .interactive-element {
                    display: none !important;
                }
            `;
            document.head.appendChild(style);
        }
    }

    /**
     * 下载生成的图片
     */
    async downloadImage(canvas, format = 'png') {
        return new Promise((resolve, reject) => {
            try {
                // 生成文件名
                const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
                const title = document.getElementById('resultTitle')?.textContent || '八字分析报告';
                const extension = format === 'jpeg' ? 'jpg' : format;
                const filename = `${title}_${timestamp}.${extension}`;

                // 确定MIME类型和质量
                const mimeType = format === 'jpeg' ? 'image/jpeg' :
                               format === 'webp' ? 'image/webp' : 'image/png';
                const quality = format === 'png' ? 1.0 : 0.9; // PNG使用无损压缩

                // 转换为blob并下载
                canvas.toBlob((blob) => {
                    if (!blob) {
                        reject(new Error('无法生成图片文件'));
                        return;
                    }

                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = filename;

                    // 触发下载
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    // 清理URL对象
                    URL.revokeObjectURL(url);

                    resolve();
                }, mimeType, quality);
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * 显示导出进度
     */
    showExportProgress(message, isSuccess = false) {
        // 清理之前的自动隐藏定时器
        if (this.autoHideTimer) {
            clearTimeout(this.autoHideTimer);
            this.autoHideTimer = null;
        }

        // 移除现有的进度提示
        this.hideExportProgress();

        // 创建进度提示
        const progressDiv = document.createElement('div');
        progressDiv.id = 'export-progress';
        progressDiv.className = `export-progress ${isSuccess ? 'success' : ''}`;
        progressDiv.innerHTML = `
            <div class="export-progress-content">
                <div class="export-progress-icon">${isSuccess ? '✅' : '🖼️'}</div>
                <div class="export-progress-text">${message}</div>
                ${!isSuccess ? '<div class="export-progress-spinner"></div>' : ''}
                ${isSuccess ? '<div class="export-progress-close" onclick="document.getElementById(\'export-progress\').remove()" title="点击关闭">×</div>' : ''}
            </div>
        `;

        // 添加样式
        if (!document.getElementById('export-progress-styles')) {
            const style = document.createElement('style');
            style.id = 'export-progress-styles';
            style.textContent = `
                .export-progress {
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: rgba(0, 0, 0, 0.9);
                    color: white;
                    padding: 20px 30px;
                    border-radius: 10px;
                    z-index: 10000;
                    text-align: center;
                    min-width: 200px;
                }
                
                .export-progress.success {
                    background: rgba(76, 175, 80, 0.9);
                }
                
                .export-progress-content {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 10px;
                }
                
                .export-progress-icon {
                    font-size: 24px;
                }
                
                .export-progress-text {
                    font-size: 16px;
                    font-weight: 500;
                }
                
                .export-progress-spinner {
                    width: 20px;
                    height: 20px;
                    border: 2px solid #ffffff40;
                    border-top: 2px solid #ffffff;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }

                .export-progress-close {
                    position: absolute;
                    top: 5px;
                    right: 10px;
                    width: 20px;
                    height: 20px;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    font-size: 14px;
                    font-weight: bold;
                    color: white;
                    transition: background 0.2s;
                }

                .export-progress-close:hover {
                    background: rgba(255, 255, 255, 0.3);
                }

                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(progressDiv);
    }

    /**
     * 显示导出错误
     */
    showExportError(message) {
        this.hideExportProgress();
        
        const errorDiv = document.createElement('div');
        errorDiv.id = 'export-error';
        errorDiv.className = 'export-progress error';
        errorDiv.innerHTML = `
            <div class="export-progress-content">
                <div class="export-progress-icon">❌</div>
                <div class="export-progress-text">${message}</div>
                <button onclick="document.getElementById('export-error').remove()" 
                        style="margin-top: 10px; padding: 5px 15px; background: #ff4444; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    确定
                </button>
            </div>
        `;
        
        document.body.appendChild(errorDiv);
    }

    /**
     * 隐藏导出进度
     */
    hideExportProgress() {
        // 清理自动隐藏定时器
        if (this.autoHideTimer) {
            clearTimeout(this.autoHideTimer);
            this.autoHideTimer = null;
        }

        const progressElement = document.getElementById('export-progress');
        const errorElement = document.getElementById('export-error');

        if (progressElement) {
            progressElement.remove();
        }

        if (errorElement) {
            errorElement.remove();
        }

        // 清理导出样式
        const exportingElements = document.querySelectorAll('.exporting-image');
        exportingElements.forEach(el => el.classList.remove('exporting-image'));
    }

    /**
     * 优化克隆的文档
     */
    optimizeClonedDocument(clonedDoc) {
        try {
            // 确保字体正确加载
            const style = clonedDoc.createElement('style');
            style.textContent = `
                * {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif !important;
                }

                /* 确保表格边框显示 */
                table, th, td {
                    border: 1px solid #ddd !important;
                    border-collapse: collapse !important;
                }

                /* 优化文本渲染 */
                .analysis-content {
                    line-height: 1.8 !important;
                    word-break: break-word !important;
                    white-space: pre-wrap !important;
                }

                /* 确保背景色 */
                body, .detailed-container, .detailed-result-container {
                    background: white !important;
                }

                /* 隐藏滚动条 */
                ::-webkit-scrollbar {
                    display: none !important;
                }
            `;
            clonedDoc.head.appendChild(style);

            // 移除可能影响导出的元素
            const elementsToRemove = [
                '.detailed-header-actions',
                '.detailed-footer',
                '.loading-state',
                '.error-state',
                '.empty-state',
                'button',
                '.interactive-element',
                'script'
            ];

            elementsToRemove.forEach(selector => {
                const elements = clonedDoc.querySelectorAll(selector);
                elements.forEach(el => el.remove());
            });

            // 优化图片
            const images = clonedDoc.querySelectorAll('img');
            images.forEach(img => {
                img.style.maxWidth = '100%';
                img.style.height = 'auto';
            });

        } catch (error) {
            console.warn('优化克隆文档时出错:', error);
        }
    }

    /**
     * 显示导出选项对话框
     */
    showExportOptions() {
        // 创建选项对话框
        const optionsDialog = document.createElement('div');
        optionsDialog.id = 'export-options-dialog';
        optionsDialog.className = 'export-options-dialog';
        optionsDialog.innerHTML = `
            <div class="export-options-content">
                <div class="export-options-header">
                    <h3>导出设置</h3>
                    <button onclick="document.getElementById('export-options-dialog').remove()" class="close-btn">×</button>
                </div>
                <div class="export-options-body">
                    <div class="option-group">
                        <label>图片格式:</label>
                        <select id="export-format">
                            <option value="png">PNG (推荐)</option>
                            <option value="jpeg">JPEG</option>
                            <option value="webp">WebP</option>
                        </select>
                    </div>
                    <div class="option-group">
                        <label>图片质量:</label>
                        <select id="export-quality">
                            <option value="2">超高清 (2x)</option>
                            <option value="1.5">高清 (1.5x)</option>
                            <option value="1">标准 (1x)</option>
                        </select>
                    </div>
                    <div class="option-group">
                        <label>图片宽度:</label>
                        <select id="export-width">
                            <option value="auto">智能适配 (推荐)</option>
                            <option value="400">超紧凑版 (400px)</option>
                            <option value="500">紧凑版 (500px)</option>
                            <option value="600">手机版 (600px)</option>
                            <option value="900">标准版 (900px)</option>
                            <option value="1200">宽屏版 (1200px)</option>
                            <option value="1600">桌面版 (1600px)</option>
                        </select>
                    </div>
                </div>
                <div class="export-options-footer">
                    <button onclick="document.getElementById('export-options-dialog').remove()" class="cancel-btn">取消</button>
                    <button onclick="window.imageExporter.exportWithOptions()" class="export-btn">导出</button>
                </div>
            </div>
        `;

        // 添加样式
        if (!document.getElementById('export-options-styles')) {
            const style = document.createElement('style');
            style.id = 'export-options-styles';
            style.textContent = `
                .export-options-dialog {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 10000;
                }

                .export-options-content {
                    background: white;
                    border-radius: 10px;
                    width: 400px;
                    max-width: 90vw;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                }

                .export-options-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 20px;
                    border-bottom: 1px solid #eee;
                }

                .export-options-header h3 {
                    margin: 0;
                    color: #333;
                }

                .close-btn {
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #999;
                    padding: 0;
                    width: 30px;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .close-btn:hover {
                    color: #333;
                }

                .export-options-body {
                    padding: 20px;
                }

                .option-group {
                    margin-bottom: 15px;
                }

                .option-group label {
                    display: block;
                    margin-bottom: 5px;
                    font-weight: 500;
                    color: #333;
                }

                .option-group select {
                    width: 100%;
                    padding: 8px 12px;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    font-size: 14px;
                }

                .export-options-footer {
                    display: flex;
                    justify-content: flex-end;
                    gap: 10px;
                    padding: 20px;
                    border-top: 1px solid #eee;
                }

                .cancel-btn, .export-btn {
                    padding: 8px 20px;
                    border: none;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 14px;
                }

                .cancel-btn {
                    background: #f5f5f5;
                    color: #666;
                }

                .cancel-btn:hover {
                    background: #e5e5e5;
                }

                .export-btn {
                    background: #4a00e0;
                    color: white;
                }

                .export-btn:hover {
                    background: #3a00b0;
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(optionsDialog);
    }

    /**
     * 使用自定义选项导出
     */
    async exportWithOptions() {
        const format = document.getElementById('export-format').value;
        const quality = parseFloat(document.getElementById('export-quality').value);
        const widthValue = document.getElementById('export-width').value;

        // 临时保存原始选项
        const originalScale = this.exportOptions.scale;

        // 更新导出选项
        this.exportOptions.scale = quality;

        // 处理宽度选项
        if (widthValue === 'auto') {
            this.customWidth = null; // 使用智能适配
        } else {
            this.customWidth = parseInt(widthValue); // 使用指定宽度
        }

        // 关闭选项对话框
        document.getElementById('export-options-dialog').remove();

        try {
            // 执行导出
            await this.exportAsImage(format);
        } finally {
            // 恢复原始选项
            this.exportOptions.scale = originalScale;
            this.customWidth = null;
        }
    }
}

// 确保在DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 创建全局实例
    window.imageExporter = new ImageExporter();

    // 全局导出函数
    window.exportAsImage = function() {
        console.log('导出长图功能被调用');
        if (window.imageExporter) {
            window.imageExporter.exportAsImage();
        } else {
            console.error('图片导出器未初始化');
        }
    };

    // 高级导出函数（带选项）
    window.exportAsImageAdvanced = function() {
        console.log('高级导出功能被调用');
        if (window.imageExporter) {
            window.imageExporter.showExportOptions();
        } else {
            console.error('图片导出器未初始化');
        }
    };

    console.log('图片导出功能已初始化');
});

// 如果DOM已经加载完成，立即初始化
if (document.readyState === 'loading') {
    // DOM还在加载中，等待DOMContentLoaded事件
} else {
    // DOM已经加载完成，立即初始化
    window.imageExporter = new ImageExporter();

    window.exportAsImage = function() {
        console.log('导出长图功能被调用');
        if (window.imageExporter) {
            window.imageExporter.exportAsImage();
        } else {
            console.error('图片导出器未初始化');
        }
    };

    window.exportAsImageAdvanced = function() {
        console.log('高级导出功能被调用');
        if (window.imageExporter) {
            window.imageExporter.showExportOptions();
        } else {
            console.error('图片导出器未初始化');
        }
    };

    console.log('图片导出功能已立即初始化');
}
