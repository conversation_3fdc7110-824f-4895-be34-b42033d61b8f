"""
改进的提示词管理器
确保正确使用五行生克制化理论和调候用神理论
"""

import os
import json
import time
import threading
from typing import Dict, Any, Optional

class ImprovedPromptManager:
    """改进的提示词管理器，确保理论正确性"""
    
    def __init__(self, prompts_dir: str = "backend/prompts"):
        self.prompts_dir = prompts_dir
        self.cache = {}
        self.cache_ttl = 3600  # 1小时缓存
        self.lock = threading.Lock()
        
        # 加载核心理论文件
        self.shishen_theory = self._load_theory_file("shishen_theory.txt")
        self.wuxing_theory = self._load_theory_file("wuxing_theory.txt")
        self.user_requirements = self._load_theory_file("user_requirements.txt")
        
    def _load_theory_file(self, filename: str) -> str:
        """加载理论指导文件"""
        try:
            file_path = os.path.join(self.prompts_dir, filename)
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"警告：无法加载理论文件 {filename}: {e}")
            return ""
    
    def _load_dimension_prompt(self, dimension: str) -> str:
        """加载维度特定的提示词"""
        try:
            # 从索引文件获取文件名
            index_path = os.path.join(self.prompts_dir, "index.json")
            with open(index_path, 'r', encoding='utf-8') as f:
                index_data = json.load(f)
            
            filename = index_data.get("prompts_index", {}).get(dimension)
            if not filename:
                return f"请分析{dimension}相关内容"
            
            file_path = os.path.join(self.prompts_dir, filename)
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"警告：无法加载维度提示词 {dimension}: {e}")
            return f"请分析{dimension}相关内容"
    
    def build_comprehensive_prompt(self, dimension: str, bazi_info: str) -> str:
        """构建包含完整理论指导的提示词"""
        
        # 检查缓存
        cache_key = f"{dimension}_{hash(bazi_info[:100])}"
        with self.lock:
            if cache_key in self.cache:
                cached_item = self.cache[cache_key]
                if time.time() - cached_item['timestamp'] < self.cache_ttl:
                    return cached_item['prompt']
        
        # 构建完整提示词
        prompt_parts = []
        
        # 1. 系统角色设定
        prompt_parts.append("""
【系统角色】
你是一位精通古典八字命理的专业分析师，以十神理论为核心分析方法，具备深厚的十神生克制化理论基础和丰富的实战经验。
你必须严格遵守传统十神理论，确保分析的准确性和专业性。
""")

        # 2. 核心理论约束（以十神为主）
        prompt_parts.append("""
【核心理论约束】
1. 以十神理论为分析核心：官杀→印→比劫→食伤→财→官杀（十神相生循环）
2. 绝对禁止违反十神生克规律的表述，如"印生财"、"比劫生财"、"财生食伤"等
3. 十神分析必须基于实际出现的十神，不得添加未出现的十神特点
4. 格局判断以月令十神为核心，结合日主强弱和用神忌神
5. 五行理论仅作为十神理论的基础支撑，不可喧宾夺主
""")

        # 3. 十神理论指导（重点）
        if self.shishen_theory:
            prompt_parts.append(f"【十神理论指导（核心）】\n{self.shishen_theory}")

        # 4. 五行理论指导（辅助）
        if self.wuxing_theory:
            prompt_parts.append(f"【五行理论指导（辅助参考）】\n{self.wuxing_theory}")
        
        # 5. 八字信息
        prompt_parts.append(f"【八字信息】\n{bazi_info}")
        
        # 6. 维度特定要求
        dimension_prompt = self._load_dimension_prompt(dimension)
        prompt_parts.append(f"【分析要求】\n{dimension_prompt}")
        
        # 7. 质量控制要求
        prompt_parts.append("""
【质量控制要求】
1. 分析前必须检查是否违反十神生克规律（重点）
2. 确保所有十神分析基于实际出现的十神（核心）
3. 以十神特征为主要分析依据，五行理论仅作辅助
4. 格局判断必须有十神理论依据
5. 避免使用过于绝对的表述
6. 保持分析的逻辑一致性
""")
        
        # 8. 输出格式要求
        prompt_parts.append("""
【输出格式要求】
- 使用标准Markdown格式
- 结构清晰，层次分明
- 使用第三人称表述
- 语言通俗易懂，避免过多专业术语
- 提供具体可操作的建议
""")
        
        # 组装最终提示词
        final_prompt = "\n\n".join(prompt_parts)
        
        # 缓存结果
        with self.lock:
            self.cache[cache_key] = {
                'prompt': final_prompt,
                'timestamp': time.time()
            }
        
        return final_prompt
    
    def validate_analysis_result(self, result: str) -> Dict[str, Any]:
        """验证分析结果的理论正确性"""
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': []
        }
        
        # 检查五行生克错误
        wuxing_errors = [
            "火生水", "土生木", "金生火", "水生土", "木生金"
        ]

        # 检查十神生克错误
        shishen_errors = [
            "印生财", "印星生财", "印星生财星",
            "比劫生财", "比肩生财", "劫财生财",
            "财生食伤", "财星生食伤", "正财生食神",
            "食伤生印", "食神生印", "伤官生印",
            "官杀生财", "正官生财", "七杀生财",
            "官生印生财", "杀生印生财"
        ]

        # 合并所有错误模式
        error_patterns = wuxing_errors + shishen_errors
        
        for pattern in error_patterns:
            if pattern in result:
                validation_result['is_valid'] = False
                if pattern in wuxing_errors:
                    validation_result['errors'].append(f"发现违反五行生克的错误表述: {pattern}")
                else:
                    validation_result['errors'].append(f"发现违反十神生克的错误表述: {pattern}")
        
        # 检查建禄格和羊刃格定义
        if "建禄格" in result or "羊刃格" in result:
            # 这里可以添加更详细的格局定义检查
            pass
        
        return validation_result
    
    def get_theory_summary(self) -> str:
        """获取理论要点摘要（以十神为核心）"""
        return """
【八字理论要点摘要（以十神为核心）】

## 十神理论（核心）
1. 十神相生：官杀→印→比劫→食伤→财→官杀
2. 十神相克：印制食伤、官杀制比劫、比劫制财、食伤制官杀、财制印
3. 格局以月令十神为核心
4. 用神以十神平衡为目标
5. 十神分析基于实际出现的十神
6. 天干十神主外在表现，地支藏干十神主内在特质

## 五行理论（辅助）
7. 五行相生：木→火→土→金→水→木（仅作十神理论的基础支撑）
8. 五行相克：木克土、土克水、水克火、火克金、金克木（仅作十神理论的基础支撑）
9. 调候原则：春暖、夏润、秋暖、冬暖（辅助十神分析）

## 分析原则
10. 以十神理论为主要分析依据
11. 绝对禁止违反十神生克规律的表述
12. 五行理论仅作为十神理论的基础支撑，不可喧宾夺主

【常见十神错误（重点避免）】
❌ 印生财（印不能直接生财）
❌ 比劫生财（比劫克财，不是相生）
❌ 财生食伤（财不能生食伤）
❌ 食伤生印（违反生克顺序）

【常见五行错误（次要避免）】
❌ 火生水、土生木、金生火、水生土、木生金

【正确表述】
✅ 十神生克：官杀生印、印生比劫、比劫生食伤、食伤生财、财生官杀
✅ 十神制化：印制食伤、官杀制比劫、比劫制财、食伤制官杀、财制印
✅ 十神特征：基于实际出现的十神分析性格和行为模式
✅ 五行生克：严格按照木→火→土→金→水的顺序（仅作辅助）
"""
