#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终优化验证
确认提示词已成功优化为以十神为核心
"""

import sys
import os

# 添加backend目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from improved_prompt_manager import ImprovedPromptManager

def final_optimization_verification():
    """最终优化验证"""
    print("🎯 八字分析系统提示词优化最终验证")
    print("=" * 60)
    
    # 创建提示词管理器
    prompt_manager = ImprovedPromptManager()
    
    # 模拟八字信息
    bazi_info = """
【八字信息】
年柱：甲子（七杀）
月柱：丙寅（正印）  
日柱：戊午（日主）
时柱：壬戌（正财）

【十神分析】
天干十神：七杀、正印、日主、正财
地支藏干十神：正官、正印比肩、伤官比肩、比肩食神伤官
"""
    
    print("\n=== 1. 系统角色优化验证 ===")
    
    # 构建提示词
    prompt = prompt_manager.build_comprehensive_prompt("性格", bazi_info)
    
    # 检查系统角色是否强调十神
    role_checks = {
        "强调十神理论为核心": "以十神理论为核心分析方法" in prompt,
        "专业分析师定位": "专业分析师" in prompt,
        "传统十神理论": "传统十神理论" in prompt
    }
    
    for check_name, result in role_checks.items():
        status = "✅" if result else "❌"
        print(f"{status} {check_name}: {result}")
    
    print("\n=== 2. 理论约束优化验证 ===")
    
    # 检查理论约束是否以十神为主
    constraint_checks = {
        "十神理论为分析核心": "以十神理论为分析核心" in prompt,
        "十神相生循环": "官杀→印→比劫→食伤→财→官杀" in prompt,
        "禁止十神错误": "印生财" in prompt and "比劫生财" in prompt,
        "五行为基础支撑": "五行理论仅作为十神理论的基础支撑" in prompt
    }
    
    for check_name, result in constraint_checks.items():
        status = "✅" if result else "❌"
        print(f"{status} {check_name}: {result}")
    
    print("\n=== 3. 理论指导顺序验证 ===")
    
    # 检查十神理论是否在五行理论之前
    shishen_pos = prompt.find("十神理论指导（核心）")
    wuxing_pos = prompt.find("五行理论指导（辅助参考）")
    
    order_checks = {
        "十神理论指导存在": shishen_pos != -1,
        "五行理论指导存在": wuxing_pos != -1,
        "十神理论在五行前": shishen_pos < wuxing_pos if shishen_pos != -1 and wuxing_pos != -1 else False,
        "十神标记为核心": "十神理论指导（核心）" in prompt,
        "五行标记为辅助": "五行理论指导（辅助参考）" in prompt
    }
    
    for check_name, result in order_checks.items():
        status = "✅" if result else "❌"
        print(f"{status} {check_name}: {result}")
    
    print(f"\n   十神理论位置: {shishen_pos}")
    print(f"   五行理论位置: {wuxing_pos}")
    
    print("\n=== 4. 质量控制优化验证 ===")
    
    # 检查质量控制是否强调十神
    quality_checks = {
        "检查十神生克规律": "检查是否违反十神生克规律（重点）" in prompt,
        "基于实际十神": "基于实际出现的十神（核心）" in prompt,
        "十神为主要依据": "以十神特征为主要分析依据" in prompt,
        "五行仅作辅助": "五行理论仅作辅助" in prompt
    }
    
    for check_name, result in quality_checks.items():
        status = "✅" if result else "❌"
        print(f"{status} {check_name}: {result}")
    
    print("\n=== 5. 理论摘要优化验证 ===")
    
    # 检查理论摘要
    theory_summary = prompt_manager.get_theory_summary()
    
    summary_checks = {
        "标题强调十神核心": "以十神为核心" in theory_summary,
        "十神理论标记核心": "十神理论（核心）" in theory_summary,
        "五行理论标记辅助": "五行理论（辅助）" in theory_summary,
        "十神错误重点避免": "常见十神错误（重点避免）" in theory_summary,
        "五行错误次要避免": "常见五行错误（次要避免）" in theory_summary,
        "以十神为主要分析依据": "以十神理论为主要分析依据" in theory_summary
    }
    
    for check_name, result in summary_checks.items():
        status = "✅" if result else "❌"
        print(f"{status} {check_name}: {result}")
    
    print("\n=== 6. 理论验证功能测试 ===")
    
    # 测试理论验证功能
    test_cases = [
        ("正确十神表述", "此人正印生比劫，性格温和有主见", True),
        ("错误印生财", "此人印生财，财运亨通", False),
        ("正确食神生财", "食神生财，有才华且善于赚钱", True),
        ("错误比劫生财", "比劫生财，自力更生", False)
    ]
    
    validation_passed = 0
    for test_name, test_content, expected_valid in test_cases:
        result = prompt_manager.validate_analysis_result(test_content)
        
        if isinstance(result, dict):
            is_valid = result.get("is_valid", True)
            
            if is_valid == expected_valid:
                status = "✅"
                validation_passed += 1
                print(f"{status} {test_name}: 验证正确")
            else:
                status = "❌"
                print(f"{status} {test_name}: 验证错误")
    
    print(f"\n   验证功能准确率: {validation_passed}/{len(test_cases)} = {validation_passed/len(test_cases)*100:.1f}%")
    
    print("\n=== 7. 提示词统计信息 ===")
    
    # 统计信息
    stats = {
        "提示词总长度": len(prompt),
        "十神理论出现次数": prompt.count("十神"),
        "五行理论出现次数": prompt.count("五行"),
        "印生财出现次数": prompt.count("印生财"),
        "比劫生财出现次数": prompt.count("比劫生财"),
        "官杀生印出现次数": prompt.count("官杀生印")
    }
    
    for stat_name, value in stats.items():
        print(f"   {stat_name}: {value}")
    
    print("\n" + "=" * 60)
    print("📋 优化验证总结:")
    
    # 计算总体通过率
    all_checks = []
    all_checks.extend(role_checks.values())
    all_checks.extend(constraint_checks.values())
    all_checks.extend(order_checks.values())
    all_checks.extend(quality_checks.values())
    all_checks.extend(summary_checks.values())
    
    passed_checks = sum(all_checks)
    total_checks = len(all_checks)
    pass_rate = passed_checks / total_checks * 100
    
    print(f"\n✅ 检查项通过率: {passed_checks}/{total_checks} = {pass_rate:.1f}%")
    print(f"✅ 理论验证准确率: {validation_passed}/{len(test_cases)} = {validation_passed/len(test_cases)*100:.1f}%")
    
    if pass_rate >= 90 and validation_passed == len(test_cases):
        print(f"\n🎉 优化成功！提示词已成功调整为以十神理论为核心")
        print(f"✅ 十神理论现在是分析的主要依据")
        print(f"✅ 五行理论已调整为辅助支撑地位")
        print(f"✅ 理论验证功能正常工作")
        print(f"✅ 系统能够有效防止'印生财'等理论错误")
        return True
    else:
        print(f"\n⚠️ 优化需要进一步完善")
        print(f"❌ 部分检查项未通过或验证功能有问题")
        return False

if __name__ == "__main__":
    success = final_optimization_verification()
    if success:
        print(f"\n🎯 确认：提示词优化完成，十神理论已成为分析重点！")
    else:
        print(f"\n⚠️ 需要进一步优化调整")
