#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化后的八字分析器
集成新的提示词管理系统
"""

import json
import os
import logging
import threading
import time
from datetime import datetime
from typing import Dict, List, Any, Optional

# 导入优化后的组件
from improved_prompt_manager import ImprovedPromptManager
from prompt_evaluator import PromptEvaluator
from prompt_version_manager import PromptVersionManager, PromptABTester
from bazi_summary import get_bazi_info, set_bazi_data
from LLMapi import call_api, API_URL, API_HEADERS

logger = logging.getLogger(__name__)

class OptimizedBaziAnalyzer:
    """优化后的八字分析器"""
    
    def __init__(self, 
                 personality_only: bool = False,
                 bazi_data: Dict = None,
                 summary: str = None,
                 version: str = "current",
                 enable_evaluation: bool = True,
                 enable_ab_testing: bool = False):
        """初始化优化后的八字分析器
        
        Args:
            personality_only: 是否只分析性格维度
            bazi_data: 八字数据
            summary: 摘要文本
            version: 使用的提示词版本
            enable_evaluation: 是否启用质量评估
            enable_ab_testing: 是否启用A/B测试
        """
        self.personality_only = personality_only
        self.bazi_data = bazi_data
        self.summary = summary
        self.version = version
        self.enable_evaluation = enable_evaluation
        self.enable_ab_testing = enable_ab_testing
        
        # 初始化组件
        self.prompt_manager = ImprovedPromptManager()
        self.evaluator = PromptEvaluator() if enable_evaluation else None
        self.version_manager = PromptVersionManager()
        self.ab_tester = PromptABTester(self.version_manager) if enable_ab_testing else None
        
        # 分析结果和评估结果
        self.analysis_results = {}
        self.evaluation_results = {}
        self.lock = threading.Lock()
    
    def get_available_dimensions(self) -> List[str]:
        """获取可用的分析维度"""
        if self.personality_only:
            return ["性格"]
        return self.prompt_manager.get_available_dimensions()
    
    def analyze_dimension(self, dimension: str, user_id: str = None) -> Dict[str, Any]:
        """分析特定维度
        
        Args:
            dimension: 分析维度
            user_id: 用户ID（用于A/B测试）
            
        Returns:
            Dict: 分析结果和评估信息
        """
        result = {
            "dimension": dimension,
            "timestamp": datetime.now().isoformat(),
            "analysis_content": "",
            "evaluation": None,
            "version_used": self.version,
            "error": None
        }
        
        try:
            print(f"🧠 开始优化分析维度: {dimension}", flush=True)
            
            # A/B测试：获取用户对应的版本
            if self.enable_ab_testing and user_id and self.ab_tester:
                experiment_name = f"{dimension}_optimization"
                variant_version = self.ab_tester.get_variant(user_id, experiment_name)
                if variant_version:
                    result["version_used"] = variant_version
                    # 临时切换到实验版本
                    temp_prompt_manager = PromptManager(version=variant_version)
                else:
                    temp_prompt_manager = self.prompt_manager
            else:
                temp_prompt_manager = self.prompt_manager
            
            # 获取八字信息
            bazi_info_text = self._get_bazi_info_text()
            
            # 构建优化后的提示词
            prompt = temp_prompt_manager.build_comprehensive_prompt(dimension, bazi_info_text)
            
            # 记录提示词（用于调试）
            self._log_prompt(f"{dimension}_{threading.get_ident()}", prompt)
            
            # 调用LLM API
            api_data = {
                "model": "deepseek/deepseek-r1-0528:free",
                "messages": [
                    {"role": "user", "content": prompt}
                ]
            }
            
            api_result = call_api(API_URL, API_HEADERS, json.dumps(api_data))
            
            # 处理API结果
            if "error" in api_result:
                result["error"] = api_result["error"]
                result["analysis_content"] = f"分析失败: {api_result['error']}"
                return result
            
            if "choices" in api_result and len(api_result["choices"]) > 0:
                analysis_content = api_result["choices"][0]["message"]["content"]
                result["analysis_content"] = analysis_content
                
                # 质量评估
                if self.enable_evaluation and self.evaluator:
                    evaluation = self.evaluator.evaluate_analysis_result(
                        analysis_content, 
                        self.bazi_data or {},
                        dimension
                    )
                    result["evaluation"] = evaluation
                    
                    # 记录A/B测试结果
                    if self.enable_ab_testing and user_id and self.ab_tester:
                        self.ab_tester.record_result(
                            user_id, 
                            experiment_name,
                            "quality_score",
                            evaluation.get("overall_score", 0.0)
                        )
                
                print(f"✅ 维度 {dimension} 分析完成", flush=True)
                if result.get("evaluation"):
                    score = result["evaluation"].get("overall_score", 0.0)
                    print(f"📊 质量评分: {score:.2f}", flush=True)
            else:
                result["error"] = "API返回格式无效"
                result["analysis_content"] = "分析失败: API返回格式无效"
            
        except Exception as e:
            error_msg = f"分析维度 {dimension} 时出错: {str(e)}"
            logger.error(error_msg)
            result["error"] = error_msg
            result["analysis_content"] = f"分析失败: {str(e)}"
        
        return result
    
    def analyze_all_dimensions(self, user_id: str = None) -> Dict[str, Any]:
        """分析所有维度
        
        Args:
            user_id: 用户ID（用于A/B测试）
            
        Returns:
            Dict: 所有维度的分析结果
        """
        dimensions = self.get_available_dimensions()
        results = {}
        
        print(f"🚀 开始分析 {len(dimensions)} 个维度", flush=True)
        
        # 并行分析各维度
        threads = []
        for dimension in dimensions:
            thread = threading.Thread(
                target=self._analyze_dimension_thread,
                args=(dimension, user_id, results),
                name=f"Thread-{dimension}"
            )
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 生成整体评估报告
        overall_evaluation = self._generate_overall_evaluation(results)
        
        return {
            "dimensions": results,
            "overall_evaluation": overall_evaluation,
            "analysis_timestamp": datetime.now().isoformat(),
            "version_used": self.version,
            "total_dimensions": len(dimensions)
        }
    
    def _analyze_dimension_thread(self, dimension: str, user_id: str, results: Dict):
        """线程安全的维度分析"""
        try:
            result = self.analyze_dimension(dimension, user_id)
            with self.lock:
                results[dimension] = result
        except Exception as e:
            logger.error(f"线程分析维度 {dimension} 失败: {str(e)}")
            with self.lock:
                results[dimension] = {
                    "dimension": dimension,
                    "error": str(e),
                    "analysis_content": f"分析失败: {str(e)}"
                }
    
    def _get_bazi_info_text(self) -> str:
        """获取八字信息文本"""
        if self.summary:
            return self.summary
        
        if self.bazi_data:
            # 线程安全地设置八字数据
            with self.lock:
                import copy
                local_bazi_data = copy.deepcopy(self.bazi_data)
                set_bazi_data(local_bazi_data)
            return get_bazi_info()
        
        return "八字信息缺失"
    
    def _log_prompt(self, identifier: str, prompt: str):
        """记录提示词到日志文件"""
        try:
            log_dir = os.path.join(os.path.dirname(__file__), "llm_logs")
            os.makedirs(log_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file = os.path.join(log_dir, f"prompt_{identifier}_{timestamp}.txt")
            
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write(f"=== 提示词日志 ===\n")
                f.write(f"标识符: {identifier}\n")
                f.write(f"时间: {datetime.now().isoformat()}\n")
                f.write(f"版本: {self.version}\n")
                f.write(f"长度: {len(prompt)} 字符\n")
                f.write(f"\n=== 提示词内容 ===\n")
                f.write(prompt)
        except Exception as e:
            logger.warning(f"记录提示词日志失败: {str(e)}")
    
    def _generate_overall_evaluation(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成整体评估报告"""
        if not self.enable_evaluation:
            return {}
        
        evaluations = []
        for dimension_result in results.values():
            if "evaluation" in dimension_result and dimension_result["evaluation"]:
                evaluations.append(dimension_result["evaluation"])
        
        if not evaluations:
            return {}
        
        # 计算平均分数
        total_score = sum(eval_result.get("overall_score", 0.0) for eval_result in evaluations)
        average_score = total_score / len(evaluations)
        
        # 统计常见问题
        all_issues = []
        for eval_result in evaluations:
            all_issues.extend(eval_result.get("issues", []))
        
        issue_counts = {}
        for issue in all_issues:
            issue_counts[issue] = issue_counts.get(issue, 0) + 1
        
        common_issues = dict(
            sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        )
        
        return {
            "average_score": average_score,
            "total_evaluations": len(evaluations),
            "common_issues": common_issues,
            "score_distribution": {
                "excellent": len([e for e in evaluations if e.get("overall_score", 0) >= 0.9]),
                "good": len([e for e in evaluations if 0.7 <= e.get("overall_score", 0) < 0.9]),
                "fair": len([e for e in evaluations if 0.5 <= e.get("overall_score", 0) < 0.7]),
                "poor": len([e for e in evaluations if e.get("overall_score", 0) < 0.5])
            },
            "recommendations": self._generate_overall_recommendations(average_score, common_issues)
        }
    
    def _generate_overall_recommendations(self, 
                                        average_score: float, 
                                        common_issues: Dict[str, int]) -> List[str]:
        """生成整体改进建议"""
        recommendations = []
        
        if average_score < 0.6:
            recommendations.append("整体分析质量偏低，建议全面优化提示词系统")
        elif average_score < 0.8:
            recommendations.append("分析质量有待提升，建议针对性优化问题较多的维度")
        
        # 基于常见问题生成建议
        for issue, count in list(common_issues.items())[:3]:
            if "十神" in issue:
                recommendations.append("重点关注十神使用的准确性，确保严格按照八字实际内容分析")
            elif "格式" in issue:
                recommendations.append("改进输出格式规范性，确保符合Markdown标准")
            elif "逻辑" in issue:
                recommendations.append("加强分析逻辑的一致性和连贯性")
        
        return recommendations
    
    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        stats = {
            "prompt_manager": self.prompt_manager.get_prompt_stats(),
            "version_info": {
                "current_version": self.version,
                "available_versions": len(self.version_manager.list_versions())
            },
            "evaluation_enabled": self.enable_evaluation,
            "ab_testing_enabled": self.enable_ab_testing
        }
        
        if self.enable_ab_testing and self.ab_tester:
            stats["active_experiments"] = len(self.ab_tester.experiments)
        
        return stats
