#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的提示词
验证十神为核心的提示词结构
"""

import sys
import os

# 添加backend目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from improved_prompt_manager import ImprovedPromptManager

def test_optimized_prompts():
    """测试优化后的提示词"""
    print("=== 测试优化后的提示词（以十神为核心） ===\n")
    
    # 创建提示词管理器
    prompt_manager = ImprovedPromptManager()
    
    # 模拟八字信息
    bazi_info = """
【八字信息】
年柱：甲子（七杀）
月柱：丙寅（正印）  
日柱：戊午（日主）
时柱：壬戌（正财）

【十神分析】
天干十神：七杀、正印、日主、正财
地支藏干十神：正官、正印比肩、伤官比肩、比肩食神伤官

【格局特点】
月令寅木为正印，印格成立
日主戊土生于寅月，身弱需要扶助
"""
    
    # 构建性格分析提示词
    prompt = prompt_manager.build_comprehensive_prompt("性格", bazi_info)
    
    print(f"📊 优化后提示词统计:")
    print(f"   - 总长度: {len(prompt)} 字符")
    
    # 检查十神重点内容
    shishen_checks = {
        "系统角色强调十神": "以十神理论为核心分析方法" in prompt,
        "核心约束以十神为主": "以十神理论为分析核心" in prompt,
        "十神理论指导在前": prompt.find("十神理论指导（核心）") < prompt.find("五行理论指导（辅助参考）"),
        "十神相生循环": "官杀→印→比劫→食伤→财→官杀" in prompt,
        "禁止印生财": "印生财" in prompt,
        "十神性格总论": "十神性格总论" in prompt,
        "天干十神优势": "天干十神优势特质" in prompt,
        "十神行为模式": "十神行为模式分析" in prompt,
        "十神发展建议": "十神发展建议" in prompt,
        "基于实际十神": "基于实际出现的十神" in prompt
    }
    
    print(f"\n🎯 十神重点内容检查:")
    all_passed = True
    for check_name, result in shishen_checks.items():
        status = "✅" if result else "❌"
        print(f"{status} {check_name}: {result}")
        if not result:
            all_passed = False
    
    # 检查五行辅助地位
    wuxing_checks = {
        "五行标记为辅助": "五行理论指导（辅助参考）" in prompt,
        "五行不可喧宾夺主": "五行理论仅作为十神理论的基础支撑，不可喧宾夺主" in prompt,
        "十神理论在五行前": prompt.find("十神理论指导") < prompt.find("五行理论指导"),
        "质量控制强调十神": "以十神特征为主要分析依据，五行理论仅作辅助" in prompt
    }
    
    print(f"\n🔧 五行辅助地位检查:")
    for check_name, result in wuxing_checks.items():
        status = "✅" if result else "❌"
        print(f"{status} {check_name}: {result}")
        if not result:
            all_passed = False
    
    # 显示提示词结构
    print(f"\n📋 提示词结构分析:")
    sections = [
        "【系统角色】",
        "【核心理论约束】", 
        "【十神理论指导（核心）】",
        "【五行理论指导（辅助参考）】",
        "【八字信息】",
        "【分析要求】",
        "【质量控制要求】",
        "【输出格式要求】"
    ]
    
    for i, section in enumerate(sections, 1):
        if section in prompt:
            position = prompt.find(section)
            print(f"{i}. {section} - 位置: {position}")
        else:
            print(f"{i}. {section} - ❌ 未找到")
    
    # 测试理论摘要
    print(f"\n=== 理论摘要测试 ===")
    theory_summary = prompt_manager.get_theory_summary()
    
    summary_checks = {
        "十神理论为核心": "十神理论（核心）" in theory_summary,
        "五行理论为辅助": "五行理论（辅助）" in theory_summary,
        "以十神为主要分析依据": "以十神理论为主要分析依据" in theory_summary,
        "十神错误重点避免": "常见十神错误（重点避免）" in theory_summary,
        "五行错误次要避免": "常见五行错误（次要避免）" in theory_summary
    }
    
    print("理论摘要内容检查:")
    for check_name, result in summary_checks.items():
        status = "✅" if result else "❌"
        print(f"{status} {check_name}: {result}")
        if not result:
            all_passed = False
    
    # 显示理论摘要片段
    print(f"\n📝 理论摘要开头:")
    print(theory_summary[:300] + "..." if len(theory_summary) > 300 else theory_summary)
    
    # 测试理论验证
    print(f"\n=== 理论验证测试 ===")
    
    test_cases = [
        ("正确十神表述", "此人正印生比劫，性格温和有主见", True),
        ("错误十神表述", "此人印生财，财运亨通", False),
        ("正确食神生财", "食神生财，有才华且善于赚钱", True),
        ("错误比劫生财", "比劫生财，自力更生赚大钱", False)
    ]
    
    for test_name, test_content, expected_valid in test_cases:
        result = prompt_manager.validate_analysis_result(test_content)
        
        if isinstance(result, dict):
            is_valid = result.get("is_valid", True)
            errors = result.get("errors", [])
            
            if is_valid == expected_valid:
                status = "✅"
                print(f"{status} {test_name}: 验证正确")
            else:
                status = "❌"
                all_passed = False
                print(f"{status} {test_name}: 验证错误")
            
            if errors:
                for error in errors:
                    print(f"   - {error}")
    
    print(f"\n=== 优化总结 ===")
    if all_passed:
        print("🎉 所有检查都通过！")
        print("\n✅ 确认：提示词已成功优化为以十神为核心")
        print("✅ 确认：五行理论已调整为辅助地位")
        print("✅ 确认：性格分析模板已优化为十神导向")
        print("✅ 确认：理论验证功能正常工作")
        print("✅ 确认：提示词结构清晰合理")
    else:
        print("❌ 部分检查未通过，需要进一步优化")
    
    return all_passed

if __name__ == "__main__":
    success = test_optimized_prompts()
    if success:
        print(f"\n🎯 优化成功！提示词现在以十神理论为核心，五行理论为辅助")
    else:
        print(f"\n⚠️ 优化需要进一步调整")
