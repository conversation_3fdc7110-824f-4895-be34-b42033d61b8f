"""
测试改进后的提示词系统
验证五行理论和调候用神的正确性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from improved_prompt_manager import ImprovedPromptManager

def test_wuxing_theory():
    """测试五行理论的正确性"""
    print("=== 测试五行理论 ===")

    manager = ImprovedPromptManager()

    # 测试用例：包含常见错误的分析结果
    test_results = [
        "此命印生财，财运亨通",  # 错误：印生财
        "官生印生财，事业财运双丰收",  # 错误：违反生克顺序
        "火生水调候，五行流通",  # 错误：火生水
        "食神生财，财运稳定",  # 正确：金生水
        "印星制食伤，食伤生财",  # 正确：间接关系
        "比劫生财，自力更生",  # 错误：比劫克财
        "财生食伤，才华横溢",  # 错误：财不生食伤
        "官杀生印，印生比劫，比劫生食伤，食伤生财",  # 正确：完整循环
    ]

    for i, result in enumerate(test_results, 1):
        print(f"\n测试用例 {i}: {result}")
        validation = manager.validate_analysis_result(result)

        if validation['is_valid']:
            print("✅ 理论正确")
        else:
            print("❌ 发现错误:")
            for error in validation['errors']:
                print(f"   - {error}")

def test_shishen_shengke():
    """测试十神生克关系"""
    print("\n=== 测试十神生克关系 ===")

    manager = ImprovedPromptManager()

    # 测试十神生克的正确表述
    shishen_tests = [
        # 正确的十神生克
        ("官杀生印", True, "权威滋养智慧"),
        ("印生比劫", True, "智慧增强能力"),
        ("比劫生食伤", True, "能力发挥才华"),
        ("食伤生财", True, "才华创造财富"),
        ("财生官杀", True, "财富带来地位"),

        # 正确的十神相克
        ("印制食伤", True, "约束表达"),
        ("官杀制比劫", True, "约束自我"),
        ("比劫制财", True, "竞争获利"),
        ("食伤制官杀", True, "才华制权"),
        ("财制印", True, "现实制约理想"),

        # 错误的十神关系
        ("印生财", False, "违反十神生克规律"),
        ("比劫生财", False, "比劫克财，不是相生"),
        ("财生食伤", False, "财不能生食伤"),
        ("食伤生印", False, "违反生克顺序"),
        ("官杀生财", False, "官杀不直接生财"),
    ]

    for relation, expected_valid, description in shishen_tests:
        print(f"\n测试关系: {relation} - {description}")
        validation = manager.validate_analysis_result(f"此命{relation}，{description}")

        is_actually_valid = validation['is_valid']

        if is_actually_valid == expected_valid:
            status = "✅ 正确" if expected_valid else "✅ 正确识别错误"
            print(f"{status}")
        else:
            status = "❌ 判断错误"
            print(f"{status}: 期望{expected_valid}，实际{is_actually_valid}")
            if validation['errors']:
                for error in validation['errors']:
                    print(f"   - {error}")

def test_prompt_building():
    """测试提示词构建"""
    print("\n=== 测试提示词构建 ===")
    
    manager = ImprovedPromptManager()
    
    # 模拟八字信息
    bazi_info = """
【八字四柱】
八字: 甲寅 丙寅 戊午 丁巳

【四柱详解】
年柱(甲寅): 天干(甲): 偏财, 地支(寅)藏干: 甲 丙 戊, 藏干十神: 偏财 食神 比肩
月柱(丙寅): 天干(丙): 食神, 地支(寅)藏干: 甲 丙 戊, 藏干十神: 偏财 食神 比肩  
日柱(戊午): 天干(戊): 日主, 地支(午)藏干: 丁 己, 藏干十神: 伤官 劫财
时柱(丁巳): 天干(丁): 伤官, 地支(巳)藏干: 丙 庚 戊, 藏干十神: 食神 正财 比肩
"""
    
    # 测试不同维度的提示词构建
    dimensions = ["性格", "健康", "日主强弱"]
    
    for dimension in dimensions:
        print(f"\n--- 测试 {dimension} 维度 ---")
        prompt = manager.build_comprehensive_prompt(dimension, bazi_info)
        
        # 检查提示词是否包含必要的理论指导
        required_elements = [
            "五行生克制化",
            "调候",
            "十神理论",
            "质量控制"
        ]
        
        missing_elements = []
        for element in required_elements:
            if element not in prompt:
                missing_elements.append(element)
        
        if missing_elements:
            print(f"❌ 缺少必要元素: {', '.join(missing_elements)}")
        else:
            print("✅ 提示词构建完整")
        
        # 显示提示词长度
        print(f"提示词长度: {len(prompt)} 字符")

def test_theory_summary():
    """测试理论摘要"""
    print("\n=== 测试理论摘要 ===")
    
    manager = ImprovedPromptManager()
    summary = manager.get_theory_summary()
    
    print("理论摘要内容:")
    print(summary)
    
    # 检查摘要是否包含关键要点
    key_points = [
        "五行相生", "五行相克", "调候", "格局", "用神", "十神"
    ]
    
    missing_points = []
    for point in key_points:
        if point not in summary:
            missing_points.append(point)
    
    if missing_points:
        print(f"❌ 摘要缺少关键要点: {', '.join(missing_points)}")
    else:
        print("✅ 理论摘要完整")

def main():
    """主测试函数"""
    print("开始测试改进后的提示词系统...")

    try:
        test_wuxing_theory()
        test_shishen_shengke()
        test_prompt_building()
        test_theory_summary()

        print("\n=== 测试总结 ===")
        print("✅ 改进后的提示词系统测试完成")
        print("主要改进:")
        print("1. 添加了完整的五行生克制化理论指导")
        print("2. 加入了调候用神理论")
        print("3. 建立了标准十神生克制化关系")
        print("4. 建立了分析结果验证机制")
        print("5. 纠正了建禄格和羊刃格的定义")
        print("6. 禁止了违反五行和十神规律的错误表述")
        print("7. 确保了理论的系统性和一致性")

    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
