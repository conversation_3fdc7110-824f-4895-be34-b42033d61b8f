<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导出成功提示测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }

        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .test-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            width: 100%;
        }

        .test-btn:hover {
            background: #0056b3;
        }

        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #2196f3;
        }

        .test-result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }

        .test-result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .countdown {
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 导出成功提示测试</h1>
            <p>测试导出成功提示的自动消失功能</p>
        </div>

        <div class="test-info">
            <h3>📋 测试说明</h3>
            <ul>
                <li>点击下方按钮模拟导出成功</li>
                <li>成功提示应该在3秒后自动消失</li>
                <li>如果自动消失失败，可以点击右上角的×关闭</li>
                <li>观察控制台输出了解详细信息</li>
            </ul>
        </div>

        <button class="test-btn" onclick="testAutoHide()">测试自动消失 (3秒)</button>
        <button class="test-btn" onclick="testManualClose()">测试手动关闭</button>
        <button class="test-btn" onclick="testMultipleShow()">测试连续显示</button>
        <button class="test-btn" onclick="clearAllProgress()">清理所有提示</button>

        <div class="test-result" id="testResult"></div>
    </div>

    <script>
        // 模拟ImageExporter类的相关方法
        class TestImageExporter {
            constructor() {
                this.autoHideTimer = null;
            }

            showExportProgress(message, isSuccess = false) {
                console.log('显示导出进度:', message, '成功:', isSuccess);
                
                // 清理之前的自动隐藏定时器
                if (this.autoHideTimer) {
                    clearTimeout(this.autoHideTimer);
                    this.autoHideTimer = null;
                    console.log('清理了之前的定时器');
                }
                
                // 移除现有的进度提示
                this.hideExportProgress();

                // 创建进度提示
                const progressDiv = document.createElement('div');
                progressDiv.id = 'export-progress';
                progressDiv.className = `export-progress ${isSuccess ? 'success' : ''}`;
                progressDiv.innerHTML = `
                    <div class="export-progress-content">
                        <div class="export-progress-icon">${isSuccess ? '✅' : '🖼️'}</div>
                        <div class="export-progress-text">${message}</div>
                        ${!isSuccess ? '<div class="export-progress-spinner"></div>' : ''}
                        ${isSuccess ? '<div class="export-progress-close" onclick="testExporter.hideExportProgress()" title="点击关闭">×</div>' : ''}
                    </div>
                `;

                // 添加样式
                if (!document.getElementById('export-progress-styles')) {
                    const style = document.createElement('style');
                    style.id = 'export-progress-styles';
                    style.textContent = `
                        .export-progress {
                            position: fixed;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            background: rgba(0, 0, 0, 0.9);
                            color: white;
                            padding: 20px;
                            border-radius: 10px;
                            z-index: 10000;
                            text-align: center;
                            min-width: 200px;
                        }
                        
                        .export-progress.success {
                            background: rgba(76, 175, 80, 0.9);
                        }
                        
                        .export-progress-content {
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            gap: 10px;
                            position: relative;
                        }
                        
                        .export-progress-icon {
                            font-size: 24px;
                        }
                        
                        .export-progress-text {
                            font-size: 16px;
                            font-weight: 500;
                        }
                        
                        .export-progress-spinner {
                            width: 20px;
                            height: 20px;
                            border: 2px solid #ffffff40;
                            border-top: 2px solid #ffffff;
                            border-radius: 50%;
                            animation: spin 1s linear infinite;
                        }
                        
                        .export-progress-close {
                            position: absolute;
                            top: -10px;
                            right: -10px;
                            width: 20px;
                            height: 20px;
                            background: rgba(255, 255, 255, 0.2);
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;
                            font-size: 14px;
                            font-weight: bold;
                            color: white;
                            transition: background 0.2s;
                        }
                        
                        .export-progress-close:hover {
                            background: rgba(255, 255, 255, 0.3);
                        }
                        
                        @keyframes spin {
                            0% { transform: rotate(0deg); }
                            100% { transform: rotate(360deg); }
                        }
                    `;
                    document.head.appendChild(style);
                }

                document.body.appendChild(progressDiv);
                console.log('创建了新的进度提示');
            }

            hideExportProgress() {
                console.log('隐藏导出进度');
                
                // 清理自动隐藏定时器
                if (this.autoHideTimer) {
                    clearTimeout(this.autoHideTimer);
                    this.autoHideTimer = null;
                    console.log('清理了自动隐藏定时器');
                }

                const progressElement = document.getElementById('export-progress');
                const errorElement = document.getElementById('export-error');

                if (progressElement) {
                    progressElement.remove();
                    console.log('移除了进度元素');
                }

                if (errorElement) {
                    errorElement.remove();
                    console.log('移除了错误元素');
                }
            }

            // 模拟完整的导出成功流程
            simulateExportSuccess() {
                console.log('开始模拟导出成功流程');
                
                // 显示成功提示
                this.showExportProgress('导出完成！', true);
                
                // 立即设置3秒后自动消失
                this.autoHideTimer = setTimeout(() => {
                    console.log('3秒定时器触发，自动隐藏');
                    this.hideExportProgress();
                }, 3000);
                
                console.log('设置了3秒自动隐藏定时器');
            }
        }

        // 创建测试实例
        const testExporter = new TestImageExporter();

        // 测试函数
        function testAutoHide() {
            showTestResult('开始测试自动消失功能...');
            testExporter.simulateExportSuccess();
            
            // 开始倒计时显示
            let countdown = 3;
            const countdownInterval = setInterval(() => {
                if (countdown > 0) {
                    showTestResult(`成功提示已显示，${countdown}秒后自动消失...`);
                    countdown--;
                } else {
                    clearInterval(countdownInterval);
                    setTimeout(() => {
                        const progressElement = document.getElementById('export-progress');
                        if (!progressElement) {
                            showTestResult('✅ 测试通过：成功提示已自动消失！');
                        } else {
                            showTestResult('❌ 测试失败：成功提示未自动消失');
                        }
                    }, 100);
                }
            }, 1000);
        }

        function testManualClose() {
            showTestResult('测试手动关闭功能...');
            testExporter.showExportProgress('导出完成！点击右上角×关闭', true);
            showTestResult('成功提示已显示，请点击右上角的×按钮手动关闭');
        }

        function testMultipleShow() {
            showTestResult('测试连续显示功能...');
            testExporter.showExportProgress('第一个提示', false);
            
            setTimeout(() => {
                testExporter.showExportProgress('第二个提示', false);
            }, 1000);
            
            setTimeout(() => {
                testExporter.showExportProgress('最终成功提示', true);
                testExporter.autoHideTimer = setTimeout(() => {
                    testExporter.hideExportProgress();
                    showTestResult('✅ 连续显示测试完成');
                }, 3000);
            }, 2000);
        }

        function clearAllProgress() {
            testExporter.hideExportProgress();
            showTestResult('已清理所有提示');
        }

        function showTestResult(message) {
            const resultDiv = document.getElementById('testResult');
            resultDiv.className = 'test-result success';
            resultDiv.innerHTML = `<div class="countdown">${message}</div>`;
            resultDiv.style.display = 'block';
            
            console.log('测试结果:', message);
        }
    </script>
</body>
</html>
