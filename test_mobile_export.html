<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手机端图片导出测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }

        .test-section h3 {
            margin-top: 0;
            color: #333;
        }

        .device-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .test-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }

        .test-btn:hover {
            background: #0056b3;
        }

        .test-result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }

        .test-result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .test-result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .mock-content {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .mock-bazi-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }

        .mock-bazi-table th,
        .mock-bazi-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }

        .mock-bazi-table th {
            background: #f8f9fa;
        }

        /* 手机端样式 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .test-container {
                padding: 15px;
            }
            
            .test-btn {
                width: 100%;
                margin: 5px 0;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>📱 手机端图片导出测试</h1>
            <p>测试手机端图片导出宽度修复效果</p>
        </div>

        <div class="test-section">
            <h3>🔍 设备信息检测</h3>
            <div class="device-info" id="deviceInfo">
                <div>屏幕宽度: <span id="screenWidth"></span>px</div>
                <div>设备类型: <span id="deviceType"></span></div>
                <div>用户代理: <span id="userAgent"></span></div>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 模拟八字分析内容</h3>
            <div class="mock-content" id="mockContent">
                <h4>八字分析报告</h4>
                <p>出生时间：1990年5月15日 14:30</p>
                <p>八字：庚午年 辛巳月 甲子日 辛未时</p>
                
                <table class="mock-bazi-table">
                    <tr>
                        <th>年柱</th>
                        <th>月柱</th>
                        <th>日柱</th>
                        <th>时柱</th>
                    </tr>
                    <tr>
                        <td>庚午</td>
                        <td>辛巳</td>
                        <td>甲子</td>
                        <td>辛未</td>
                    </tr>
                    <tr>
                        <td>七杀</td>
                        <td>正官</td>
                        <td>日主</td>
                        <td>正官</td>
                    </tr>
                </table>

                <h5>性格分析</h5>
                <p>此人性格温和，具有很强的责任心和使命感。正官透干，做事有条理，善于管理。七杀在年柱，早年可能经历一些挫折，但这些经历会让其更加坚强。</p>

                <h5>事业运势</h5>
                <p>适合从事管理类工作，或者需要严谨态度的职业。正官当令，事业发展稳定，容易得到上级的认可和提拔。</p>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 导出测试</h3>
            <button class="test-btn" onclick="testExportWidth()">测试智能宽度适配</button>
            <button class="test-btn" onclick="testMobileDetection()">测试手机端检测</button>
            <button class="test-btn" onclick="testExportStyles()">测试导出样式应用</button>
            <button class="test-btn" onclick="simulateExport()">模拟完整导出流程</button>
            <button class="test-btn" onclick="testAutoHideSuccess()">测试成功提示自动消失</button>
            
            <div class="test-result" id="testResult"></div>
        </div>

        <div class="test-section">
            <h3>📋 测试结果</h3>
            <div id="testResults">
                <p>点击上方按钮开始测试...</p>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script>
        // 初始化设备信息
        function initDeviceInfo() {
            document.getElementById('screenWidth').textContent = window.innerWidth;
            document.getElementById('deviceType').textContent = window.innerWidth <= 768 ? '手机端' : '桌面端';
            document.getElementById('userAgent').textContent = navigator.userAgent.substring(0, 50) + '...';
        }

        // 测试导出宽度计算 - 更新版本
        function testExportWidth() {
            const element = document.getElementById('mockContent');
            const rect = element.getBoundingClientRect();
            const isMobile = window.innerWidth <= 768;

            let actualWidth = rect.width;

            if (isMobile) {
                // 智能适配手机屏幕宽度
                const screenWidth = window.innerWidth;
                const devicePixelRatio = window.devicePixelRatio || 1;

                let optimalWidth;
                if (screenWidth <= 375) {
                    optimalWidth = 750; // 2倍屏幕宽度
                } else if (screenWidth <= 414) {
                    optimalWidth = 828; // 约2倍屏幕宽度
                } else {
                    optimalWidth = Math.min(screenWidth * 2, 900);
                }

                optimalWidth = Math.ceil(optimalWidth * Math.min(devicePixelRatio, 2));
                actualWidth = Math.max(actualWidth, optimalWidth);
            }

            const minExportWidth = isMobile ? 600 : 800;
            actualWidth = Math.max(actualWidth, minExportWidth);

            showTestResult('success', `
                智能导出宽度计算测试通过！<br>
                - 元素实际宽度: ${rect.width}px<br>
                - 是否手机端: ${isMobile}<br>
                - 屏幕宽度: ${window.innerWidth}px<br>
                - 设备像素比: ${window.devicePixelRatio || 1}<br>
                - 智能计算宽度: ${actualWidth}px<br>
                - 宽度合理性: ${isMobile ? (actualWidth <= 900 ? '✅ 适中' : '⚠️ 偏宽') : '✅ 桌面端'}
            `);
        }

        // 测试手机端检测
        function testMobileDetection() {
            const isMobile = window.innerWidth <= 768;
            const isTouch = 'ontouchstart' in window;
            
            showTestResult('success', `
                手机端检测结果：<br>
                - 屏幕宽度检测: ${window.innerWidth}px (${isMobile ? '手机端' : '桌面端'})<br>
                - 触摸支持: ${isTouch ? '支持' : '不支持'}<br>
                - 最终判断: ${isMobile ? '手机端' : '桌面端'}
            `);
        }

        // 测试导出样式应用
        function testExportStyles() {
            const element = document.getElementById('mockContent');
            const isMobile = window.innerWidth <= 768;
            
            if (isMobile) {
                // 模拟应用导出样式
                element.style.width = '1000px';
                element.style.minWidth = '1000px';
                element.style.maxWidth = 'none';
                
                setTimeout(() => {
                    const newWidth = element.getBoundingClientRect().width;
                    showTestResult('success', `
                        手机端导出样式应用测试：<br>
                        - 样式应用前宽度: ${element.dataset.originalWidth || '未记录'}px<br>
                        - 样式应用后宽度: ${newWidth}px<br>
                        - 宽度是否达到1000px: ${newWidth >= 1000 ? '✅' : '❌'}
                    `);
                    
                    // 恢复原始样式
                    element.style.width = '';
                    element.style.minWidth = '';
                    element.style.maxWidth = '';
                }, 100);
            } else {
                showTestResult('success', '桌面端无需特殊样式处理');
            }
        }

        // 模拟完整导出流程
        function simulateExport() {
            const element = document.getElementById('mockContent');
            const isMobile = window.innerWidth <= 768;

            showTestResult('success', '开始模拟智能导出流程...');

            // 计算智能宽度
            let exportWidth;
            if (isMobile) {
                const screenWidth = window.innerWidth;
                if (screenWidth <= 375) {
                    exportWidth = 750;
                } else if (screenWidth <= 414) {
                    exportWidth = 828;
                } else {
                    exportWidth = Math.min(screenWidth * 2, 900);
                }
            }

            // 模拟html2canvas导出
            html2canvas(element, {
                scale: 2,
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                width: exportWidth,
                height: undefined
            }).then(canvas => {
                showTestResult('success', `
                    智能导出模拟完成！<br>
                    - Canvas宽度: ${canvas.width}px<br>
                    - Canvas高度: ${canvas.height}px<br>
                    - 实际显示宽度: ${canvas.width / 2}px (scale=2)<br>
                    - 宽度适配性: ${isMobile ? (canvas.width <= 1800 ? '✅ 适中' : '⚠️ 偏宽') : '✅ 桌面端'}<br>
                    - 空白区域: ${isMobile ? '✅ 已优化' : 'N/A'}
                `);
            }).catch(error => {
                showTestResult('error', `导出失败: ${error.message}`);
            });
        }

        // 测试成功提示自动消失
        function testAutoHideSuccess() {
            showTestResult('success', '模拟导出成功提示...');

            // 创建模拟成功提示
            const mockSuccess = document.createElement('div');
            mockSuccess.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(76, 175, 80, 0.9);
                color: white;
                padding: 20px;
                border-radius: 8px;
                z-index: 10000;
                text-align: center;
                min-width: 200px;
            `;
            mockSuccess.innerHTML = '✅ 导出完成！';
            document.body.appendChild(mockSuccess);

            // 3秒后自动消失
            setTimeout(() => {
                if (mockSuccess.parentNode) {
                    mockSuccess.parentNode.removeChild(mockSuccess);
                    showTestResult('success', '✅ 成功提示已在3秒后自动消失！');
                }
            }, 3000);

            showTestResult('success', '成功提示已显示，将在3秒后自动消失...');
        }

        // 显示测试结果
        function showTestResult(type, message) {
            const resultDiv = document.getElementById('testResult');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            resultDiv.style.display = 'block';
            
            // 添加到测试结果历史
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `<div style="margin-bottom: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                <strong>[${timestamp}]</strong><br>${message}
            </div>`;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initDeviceInfo();
            
            // 记录原始宽度
            const element = document.getElementById('mockContent');
            element.dataset.originalWidth = element.getBoundingClientRect().width;
        });

        // 窗口大小改变时更新设备信息
        window.addEventListener('resize', initDeviceInfo);
    </script>
</body>
</html>
